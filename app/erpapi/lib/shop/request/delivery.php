<?php
/**
 * 发货单处理
 *
 * @category 
 * @package 
 * <AUTHOR>
 * @version $Id: Z
 */
class erpapi_shop_request_delivery extends erpapi_shop_request_abstract
{
    protected $_delivery_errcode = array(
        'W90010'=>'已经出库',
        'W90011'=>'参数错误',
        'W90012'=>'已经出库',
        'W90013'=>'参数错误',
        'W90014'=>'参数错误',
    );

    /**
     * 发货确认
     *
     * @return void
     * <AUTHOR> @param Array $sdf = array(
     *                  'delivery_id' => #发货单ID#
     *                  'delivery_bn' => #发货单编号#
     *                  'status'      => #发货单状态#
     *                  'logi_id' => #物流公司ID#
     *                  'logi_name' => #物流公司名称#
     *                  'logi_no' => #运单号#
     *                  'logi_type' => #物流公司类型#
     *                  'feature' => #唯一码#
     *                  'is_split' => #是否拆单#
     *                  'split_model' => #拆单模式#
     *                  'is_cod' => #货到付款#
     *                  'oid_list'=> #子单#
     *                  'itemNum' => #包裹数#
     *                  'delivery_time' => #发货时间#
     *                  'last_modified' => #最后更新时间#
     *                  'delivery_cost_actual' => #物流费#
     *                  'create_time' => #创建时间#
     *                  'is_protect' => #是否保价#
     *                  'delivery' => #配送方式#
     *                  'memo' => #备注#
     *                  'is_virtual' => #是否为虚拟化发货#
     *                  'delivery_items' => array(
     *                      'bn' => #货号#
     *                      'number' => #数量#
     *                      'name' => #名称#
     *                      'item_type' => #明细类型#
     *                      'shop_goods_id' => #前端商品#
     *                      'promotion_id' => #优惠ID#
     *                  ),
     *                  'consignee' => array(
     *                      'name' => #收货人姓名#
     *                      'area' => #收货地区#
     *                      'addr' => #收货人地址#
     *                      'zip'  => #收货人邮编#
     *                      'email' => #收货人邮箱#
     *                      'mobile' => #收货人手机#
     *                      'telephone' => #收货人电话#
     *                  ),
     *                  'memberinfo' => array(
     *                      'uname' => #会员名#
     *                  ),
     *                  'orderinfo' => array(
     *                         'order_id' => #订单ID#
     *                         'order_bn' => #订单编号#
     *                         'ship_status' => #发货状态#
     *                         'createway' => #订单生成方式#
     *                         'sync'  => #回写状态#
     *                         'sellermemberid' => #买家ID#
     *                         'is_cod' => #到付#
     *                         'self_delivery' => #自发货#
     *                         'order_objects' => array(
     *                              'bn' => #货号#
     *                              'oid' => #子单编号#
     *                              'shop_goods_id' => #平台商品ID#
     *                              'quantity' => #数量#
     *                              'name' => #商品名称#
     *                              'obj_type' => #类型#
     *                              'order_items' => array(
     *                                  'bn' => #货号#
     *                                  'shop_goods_id' => #平台商品ID#
     *                                  'sendnum' => #发货数量#
     *                                  'product_name' => #商品名称#
     *                                  'promotion_id' => #优惠ID#
     *                                  'item_type' => #类型#
     *                                  'nums' => #购买数量#
     *                               ),
     *                          ),
     *                  ),
     *              )
     **/
    public function confirm($sdf,$queue=false)
    {
        $logoData['sdf'] = $sdf;
        
        // 只处理已发货与部分发货状态
        if ($sdf['status'] != 'succ' && !in_array($sdf['orderinfo']['ship_status'], array('1','2'))) return $this->succ('发货单未发货');

        $args[0] = $sdf;
        $_in_mq = $this->__caller->caller_into_mq('delivery_confirm','shop',$this->__channelObj->channel['shop_id'],$args,$queue);
        if ($_in_mq) {
            return $this->succ('成功放入队列');
        }

        $this->format_confirm_sdf($sdf);
        // ----- start <AUTHOR> @Description 检查发货obj_oid是否重复 @Date 20200409
        $params = $this->get_confirm_params($sdf);

        $logoData['params'] = $params;

        if(!empty($params['oid_list'])){
            // $tempOldList = explode(',',$params['oid_list']);
            $tempOldList = [];
            $omdDlyInfo = app::get('ome')->model('delivery')->dump(['logi_no'=>$params['logistics_no']],'delivery_id');
            if($omdDlyInfo){
                $orderObjIds = app::get('ome')->model('delivery_items_detail')->getList('order_obj_id',['delivery_id'=>$omdDlyInfo['delivery_id']]);
                if($orderObjIds){
                    $objIds = array_column($orderObjIds,'order_obj_id');
                    $objOidList = app::get('ome')->model('order_objects')->getList('oid,send_flag',['obj_id'=>$objIds]);
                    foreach ($objOidList as $objItem){
                        if(empty($objItem['oid']) || (int)$objItem['oid']<=0) continue;
                        if($objItem['send_flag'] == 'false' && $objItem['obj_type'] != 'pkg'){
                            $tempOldList[] = $objItem['oid'];
                        }
                    }
                    if(empty($tempOldList)) return $this->succ('oid已经回写过了');

                    $params['oid_list'] = implode(',',$tempOldList);
                }
            }

            unset($tempOldList,$omdDlyInfo,$orderObjIds,$objOidList);
        }
        $logoData['new_oid_list'] = $params['oid_list'];

        // ----- end
        // kernel::single('base_customlog')->saveLogByAppend('debug_request_shop_delivery_confirm',$logoData,'confirm');

        // 发货记录
        $opInfo = kernel::single('ome_func')->getDesktopUser();
        $log_id = uniqid($_SERVER['HOSTNAME']);
        $log = array(
            'shopId'           => $this->__channelObj->channel['shop_id'],
            'ownerId'          => $opInfo['op_id'],
            'orderBn'          => $sdf['orderinfo']['order_bn'],
            'deliveryCode'     => $sdf['logi_no'],
            'deliveryCropCode' => $sdf['logi_type'],
            'deliveryCropName' => $sdf['logi_name'],
            'receiveTime'      => time(),
            'status'           => 'send',
            'updateTime'       => '0',
            'message'          => '',
            'log_id'           => $log_id,
        );

        $shipmentLogModel = app::get('ome')->model('shipment_log');
        $orderModel = app::get('ome')->model('orders');

        # 全额退款的，不予回写 ------
        $orderInfo = $orderModel->dump(array('order_bn'=>$sdf['orderinfo']['order_bn']), 'order_id,pay_status, order_bn, sync,shop_type,payed');
        $refund_apply = app::get('ome')->model('refund_apply')->dump(array('order_id'=>$orderInfo['order_id'],'status'=>array('0','1','2','4','5','6')), 'money,status,refund_apply_bn');
        if (($orderInfo && $orderInfo['pay_status'] == '5') || $orderInfo['payed'] == $refund_apply['money']) {
            //1、插入同步表
            $log['status'] = 'fail';
            $log['message'] = '平台订单已售前全额退款，禁止发货，请进行拦截.';
            $log['updateTime'] = time();
            if($refund_apply['status'] != '3'){
                $log['message'] = '对应退款申请单号尚未进行拒绝，如不拒绝请追回商品，如拒绝请拒绝后再进行发货回传:退款申请单'.$refund_apply['refund_apply_bn'].'状态:'.$refund_apply['status'];
            }
            $shipmentLogModel->insert($log);
            //2、更新订单状态
            $orderModel->update(array('sync' => 'fail'),array('order_bn' => $sdf['orderinfo']['order_bn']));
            //3、截断
            kernel::single('base_customlog')->log($orderInfo, "debug/no_delivery_confirm");
            return $this->succ('no_delivery_confirm');
        }

        $shipmentLogModel->insert($log);

        // 更新订单状态
        $orderModel->update(array('sync'=>'run'),array('order_id'=>$sdf['orderinfo']['order_id']));

        // 整理参数格式
        $title = sprintf('发货状态回写[%s]-%s',$sdf['delivery_bn'],$this->__channelObj->channel['node_type']);


        $callback = array(
           'class' => get_class($this),
           'method' => 'confirm_callback',
           'params' => array(
                'shipment_log_id' => $log_id,
                'order_id'        => $sdf['orderinfo']['order_id'],
                'logi_no'         => $sdf['logi_no'],
                'obj_bn' => $sdf['orderinfo']['order_bn'],
                'company_code'=>$sdf['logi_type'],
                'delivery_bn'=>$sdf['delivery_bn'],
                'logi_name'=>$sdf['logi_name'],
                'logistics_no'=>$params['logistics_no'],
                'request_param'=>$params // 请求的参数也回传过来
            ),

        );
        kernel::single('base_customlog')->saveLogByAppend('child_father_shipment_log',['params'=>$params,'sdf'=>$sdf],'request_start');

        $result = $this->__caller->call($this->get_delivery_apiname($sdf), $params, $callback, $title,10,$sdf['orderinfo']['order_bn']);
        if(in_array($this->__channelObj->channel['node_type'],['kuaishou'])){
            $this->batchSyncChildLogisticNo($params);
        }

        kernel::single('base_customlog')->saveLogByAppend('child_father_shipment_log',['res'=>$result,'params'=>$params,'callback'=>$callback,'sdf'=>$sdf],'request');

        if ($result['rsp'] == 'fail') {
            $shipmentLogModel->update(array('status'=>'fail','message'=>$result['err_msg'],'updateTime'=>time()),array('log_id'=>$log_id));
        }

        return $result;
    }

    /**
     * 同步子运单信息（一个发货单可能存在多个子运单）
     * <AUTHOR>
     * @param $params array 需要上传平台的参数，
         $params示例：
         ---------------------------
         tid=230215-444963265310049
         company_code=ZTO
         company_name=pdd中通速递
         logistics_no=78322229080399
         package_type=normal
         ---------------------------
     * @return bool
     **/
    private function syncChildLogisticNo($params)
    {

        // 日志标题
        $title = sprintf('发货状态回写_子运单[%s]-%s', $params['logistics_no'], $this->__channelObj->channel['node_type']);

        // 矩阵回传oms相关信息
        $callback = array(
            'class' => get_class($this),
            'method' => 'childlogi_confirm_callback',
            'params' => $params,
        );

        kernel::single('base_customlog')->saveLogByAppend('child_shipment_log',['title'=>$title,'params'=>$params, 'callback'=>$callback],'request_start');
        $result = $this->__caller->call($this->get_delivery_apiname(), $params, $callback, $title, 10, $params['tid']);
        kernel::single('base_customlog')->saveLogByAppend('child_shipment_log',['params'=>$params,'res'=>$result],'request');

        if ($result['rsp'] == 'fail') {
            kernel::single('base_customlog')->saveLogByAppend('child_shipment_log',['params'=>$params,'res'=>$result],'request_fail');
            return false;
        }else{
            return true;
        }
    }

    /**  参数示例：
    $fatherParams = array(
            'tid'=>$params['tid'],
            'company_code'=>$params['company_code'],
            'company_name'=>$params['company_name'],
            'logistics_no'=>$params['logistics_no'],
     )
     **/
    /**
     * 同步子运单信息（一个发货单可能存在多个子运单）
     * <AUTHOR>
     * @param $fatherLogiParams array 父运单信息，
            $fatherLogiParams示例：
            ---------------------------
            array(
                'tid'=>$params['tid'],
                'company_code'=>$params['company_code'],
                'company_name'=>$params['company_name'],
                'logistics_no'=>$params['logistics_no'],
            )
            ---------------------------
     * @return bool
     **/
    public function batchSyncChildLogisticNo($fatherLogiParams)
    {
        // 保存日志
        kernel::single('base_customlog')->saveLogByAppend('child_shipment_log',['params'=>$fatherLogiParams],'batch_request');
        if(empty($fatherLogiParams['logistics_no'])){
            return false;
        }

        // 查询越海子运单
        $yhlogiObj = app::get('ome')->model('delivery_yhlogi');
        $yhlogiList = $yhlogiObj->getList('*', ['order_bn' => $fatherLogiParams['tid']]);
        kernel::single('base_customlog')->saveLogByAppend('child_shipment_log',['yhlogiList'=>$yhlogiList],'batch_request');

        if(!empty($yhlogiList)){
            foreach ($yhlogiList as $yhlogi){
                // 排除和父运单运单号相同的子运单
                if($fatherLogiParams['logistics_no'] != $yhlogi['trackingNumber']) {

                    $item = $fatherLogiParams;
                    $item['logistics_no'] = $yhlogi['trackingNumber'];
                    $item['package_type'] = 'break';

                    // 子运单一个一个同步给平台
                    $this->syncChildLogisticNo($item);
                }
            }
        }
    }

    /**
     * 子运单回调oms（一个发货单可能存在多个子运单）
     * <AUTHOR>
     * @param $response array 回调信息
     * @param $callback_params array 回调信息
     * @return array
     **/
    public function childlogi_confirm_callback($response=[], $callback_params=[]){

        kernel::single('base_customlog')->saveLogByAppend('child_shipment_log',['response'=>$response,'callback_params'=>$callback_params],'response');
        return $this->succ('子运单回调oms成功');
    }

    /**
     * 发货请求参数（以淘宝做为标准）
     *
     * @return void
     * <AUTHOR>
    protected function get_confirm_params($sdf)
    {
        $param = array(
            'tid'          => $sdf['orderinfo']['order_bn'], // 订单号
            'company_code' => $sdf['logi_type'], // 物流编号
            'company_name' => $sdf['logi_name'], // 物流公司
            'logistics_no' => $sdf['logi_no'], // 运单号
        );

        if($param['logistics_no']) {
            $logi_no_first = substr($param['logistics_no'], 0, 1);
            $company_code = strtoupper($param['company_code']);
            if (strpos($company_code, 'SF') !== false && $logi_no_first == '7') {
                $param['company_code'] = "ZTO";
                $param['company_name'] = "中通";
            }
        }

        if ($param['company_code'] == 'BEST') {
            $param['company_code'] = 'BESTQJT';
        }

        if ($param['company_code'] == 'EMS') {
            $param['company_code'] = 'YZDSBK';
        }
        
        return $param;
    }

    /**
     * 数据处理
     *
     * @return void
     * <AUTHOR>
    protected function format_confirm_sdf(&$sdf)
    {
        // 物流发货单去BOM头
        $pattrn           = chr(239).chr(187).chr(191);
        $sdf['logi_no']   = trim(str_replace($pattrn, '', $sdf['logi_no']));
        $sdf['logi_type'] = trim($sdf['logi_type']);
        $sdf['logi_name'] = strval($sdf['logi_name']);
        $sdf['logi_no']   = strval($sdf['logi_no']);
    }

    /**
     * 发货回调
     *
     * @return void
     * <AUTHOR>
    public function confirm_callback($response, $callback_params)
    {

        kernel::single('base_customlog')->saveLogByAppend('debug_request_shop_delivery_confirm',['response'=>$response,'callback_params'=>$callback_params],'confirm_callback');

        $rsp             = $response['rsp'];
        $msg_id          = $response['msg_id'];
        $res             = trim($response['res']);
        $err_msg         = $response['err_msg'];
        $order_id        = $callback_params['order_id'];
        $shipment_log_id = $callback_params['shipment_log_id'];
        $logi_no         = $callback_params['logi_no'];
        $delivery_bn         = $callback_params['delivery_bn'];


        $orderModel    = app::get('ome')->model('orders');
        $order = $orderModel->getList('sync,shop_type,order_bn',array('order_id'=>$order_id),0,1);

        // 已经回写成功，不需要再改
        if ($order[0]['sync'] == 'succ') $rsp = 'succ';

        // 出现需要重新的，重置
        if ($order[0]['shop_type'] == 'taobao' && false !== strstr($err_msg,'error_response')) {
            $errmsg = @json_decode($err_msg,true);
            if (is_array($errmsg)){
                $err_msg = $errmsg['error_response']['sub_msg'];

                if ($errmsg['error_response']['sub_code'] == 'B150') $rsp = 'fail';
            }
        }
        $rsp=='success' ? 'succ' : $rsp;
        $status = 'succ'; $sync_fail_type = 'none';
        $message = $err_msg.'('.$msg_id.')';

        // ERP没有发起成功且请求失败
        if ($rsp != 'succ' ) {

            $status = 'fail';

            // 错误信息
            // $message = $err_msg.'('.$msg_id.')';

            // 失败类型
            if ('已经出库' == $this->_delivery_errcode[$res]) {
                $status = 'succ';
                $message = '已经出库'.$res.'('.$msg_id.')';

                $sync_fail_type = 'shipped';
            }elseif (in_array($res,array('W90011','W90013','W90014'))) {
                $sync_fail_type = 'params';
            }

        }

        // ---- start 配合自订单的回写标记的回调处理 @Date 20200409
        if($response['rsp']=='succ' && $callback_params['method']=='store.logistics.offline.send'){
            // 如果是成功，就将发货单的里的销售物料的子单号标记回写成功
            $omdDlyInfo = app::get('ome')->model('delivery')->dump(['logi_no'=>$logi_no],'delivery_id');
            if($omdDlyInfo){
                $orderObjIds = app::get('ome')->model('delivery_items_detail')->getList('order_obj_id',['delivery_id'=>$omdDlyInfo['delivery_id']]);
                if($orderObjIds){
                    $objIds = array_column($orderObjIds,'order_obj_id');
                    app::get('ome')->model('order_objects')->update(['send_flag'=>'true'],['obj_id'=>$objIds]);
                }
            }
        }
        // ---- end

        //保存发货单的回写状态
        $deliverySyncMdl = app::get('ome')->model('delivery_sync');
        $deliverySyncMdl->update(array('sync'=>$status), array('delivery_bn'=>$delivery_bn,'sync|noequal'=>'succ'));

        // 更新订单状态
        if ($order_id) {
            // 是否有回写失败的拆分发货单
            $fail_delivery = $deliverySyncMdl->dump(array('order_id'=>$order_id,'sync|noequal'=>'succ'),'order_bn,delivery_bn,status,sync');

            $updateOrderData = array(
                // 'sync'           => empty($fail_delivery) ? $status : 'fail',
                'sync'           => $status,
                'up_time'        => time(),
            );
            $orderModel->update($updateOrderData,array('order_id'=>$order_id,'sync|noequal'=>'succ'));
        }

        $shipmentModel = app::get('ome')->model('shipment_log');
        // 更新发货日志状态
        if ($shipment_log_id) {
            $updateShipmentData = array(
                'status'     => $status,
                'updateTime' => time(),
                'message'    => $message,
            );

            $shipmentModel->update($updateShipmentData,array('log_id'=>$shipment_log_id));
        }

        return $this->callback($response, $callback_params);
    }

    /**
     * 家装服务商
     *
     * @return void
     * <AUTHOR>
    public function jzpartner_query($sdf)
    {
        $title = sprintf('家装服务商查询[%s]', $sdf['orderinfo']['order_bn']);

        $params = array(
            'tid' => $sdf['orderinfo']['order_bn'],
        );

        $result = $this->__caller->call(SHOP_WLB_ORDER_JZ_QUERY, $params, null, $title, 10, $sdf['orderinfo']['order_bn']);
        $jzdata = array();
        if ($result['rsp'] == 'succ') {
            $data = json_decode($result['data'], true);
            $data = $data['result'];
            if ($sdf['logi_type'] == 'o2o_ship') {
                foreach ($data['lg_cps']['lgcps'] as $lgcps) {
                    $code = explode('@', $lgcps['tp_code']);

                    // 返回第一个
                    if ($sdf['logi_type'] == current($code)) {
                        $jzdata['lg_tp_dto'] = $lgcps;
                        break;
                    }
                }
                if (empty($jzdata['lg_tp_dto'])) {
                    $jzdata['lg_tp_dto'] = $data['lg_cps']['lgcps'][0];
                }
            } else {
                foreach ($data['expresses']['expresses'] as $lgcps) {
                    $code = $lgcps['code'];

                    // 返回第一个
                    if ($sdf['logi_type'] == $code) {
                        $jzdata['lg_tp_dto'] = $lgcps;
                        break;
                    }
                }
                if (empty($jzdata['lg_tp_dto'])) {
                    $jzdata['lg_tp_dto'] = $data['expresses']['expresses'][0];
                }
            }

            if ($data['support_install'] == '1') {
                $jzdata['ins_tp_dto'] = $data['ins_tps']['instps'][0];

            }

        }

        return $jzdata;
    }

    /**
     * 获取发货接口(默认线下发货)
     *
     * @return void
     * <AUTHOR>
    protected function get_delivery_apiname($sdf)
    {
        return SHOP_LOGISTICS_OFFLINE_SEND;
    }

    /**
     * 添加发货单
     *
     * @return void
     * <AUTHOR>
    public function add($sdf){}

    /**
     * 更新发货单流水状态
     *
     * @return void
     * <AUTHOR>
    public function process_update($sdf){}

    /**
     * 更新物流公司
     *
     * @return void
     * <AUTHOR>
    public function logistics_update($sdf){}

}