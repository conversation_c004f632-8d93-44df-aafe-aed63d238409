<?php
/**
* 退款单 版本一
*
* @category apibusiness
* @package apibusiness/response/refund
* <AUTHOR>
* @version $Id: v1.php 2013-3-12 17:23Z
*/
class apibusiness_response_aftersalev2_amazon_v1 extends apibusiness_response_aftersalev2_v1
{

    /**
     * 验证是否接收
     *
     * @return void
     * <AUTHOR>
    protected function canAccept($tgOrder=array())
    {
        return parent::canAccept($tgOrder);
    }


    /**
     * 添加退款单
     *
     * @return void
     * <AUTHOR>
    public function add()
    {
        parent::add();

        
    }

   
}