<?php
/**
* 360buy接口请求实现
*
* @category apibusiness
* @package apibusiness/lib/request/v1
* <AUTHOR>
* @version $Id: 360buy.php 2013-13-12 14:44Z
*/
class apibusiness_request_v1_360buy extends apibusiness_request_partyabstract
{
    /**
     * 获取发货参数
     *
     * @param Array $delivery 发货单信息
     * @return Array
     * <AUTHOR>
    protected function getDeliveryParam($delivery)
    {
        $param = array(
            'tid'          => $delivery['order']['order_bn'],
            'company_code' => $delivery['dly_corp']['type'],
            'company_name' => $delivery['logi_name'] ? $delivery['logi_name'] : '',
            'logistics_no' => $delivery['logi_no'] ? $delivery['logi_no'] : '',
            '360buy_business_type' => $this->_shop['addon']['type'],
        );
        if ($this->_shop['addon']['type'] == 'SOPL') {
            $param['package_num'] = $delivery['itemNum'];
        }
        return $param;
    }// TODO TEST

    

    /**
     * @根据矩阵返回的错误码，表述具体京东请求的返回消息内容
     * @access public
     * @param void
     * @return void
     */
    public function jdErrorMsg($code)
    {
        $errormsgs = array(
                    'w06000'=>'成功',
                    'w06001'=>'其他',
                    'w06101'=>'已经出库',
                    'w06102'=>'出库订单不存在或已被删除',
                    'w06104'=>'订单状态不为等待发货',
                    'w06105'=>'订单已经发货',
                    'w06106'=>'正在出库中',
        );
        return isset($errormsgs[$code]) ? $errormsgs[$code] : '其他';
    }

    
    
    
    protected function format_aftersale_params($aftersale,$status){
        $params = array(
                
            'refund_id'=>$aftersale['return_bn'],
        );
        return $params;
    }

    protected function aftersale_api($status){
        $api_method = '';
        switch( $status ){
            case '4':
                $api_method = CHECK_REFUND_GOOD;
            break;
            
        }
        return $api_method;
    }
    
    public function update_order_shippinginfo($order)
    {
        
    }

    public function add_delivery_callback($result)
    {
        $status = $result->get_status();
        $err_msg = $result->get_err_msg();
        $msg_id = $result->get_msg_id();
        
        $request_params = $result->get_request_params();
        $failApiModel = app::get('erpapi')->model('api_fail');
        $tid = $request_params['tid'];
        if($request_params['company_code'] == 'JDCOD'){
            $sql = "SELECT d.delivery_bn
                FROM sdb_ome_delivery_order as deo
                LEFT JOIN sdb_ome_delivery AS d ON deo.delivery_id = d.delivery_id LEFT JOIN sdb_ome_orders as o ON o.order_id=deo.order_id
                WHERE (d.parent_id=0 OR d.is_bind='true')
                AND d.disabled='false'
                AND d.status='succ' AND o.order_bn='".$tid."'";
            $delivery = $failApiModel->db->selectrow($sql);
            if($status == 'succ'){
                
                $failApiModel->publish_api_fail(LOGISTICS_OFFLINE_RPC,array( 'obj_bn'=>$delivery['delivery_bn'],'obj_type'=>'JDDELIVERY'),array('rsp'=>'succ'));
            }
            
            if($status == 'fail' && ($err_msg == '运单没有在青龙系统生成' || $err_msg == '平台连接后端服务不可用')){
                $failparams = array(
                    'obj_bn'=>$delivery['delivery_bn'],
                    'obj_type'=>'JDDELIVERY',
                );
                $failresult = array(
                    'rsp'=>'fail',
                    'err_msg'=>$err_msg,
                    'msg_id'=>$msg_id,
                    'res'=>'e00090',
                );
                $failApiModel->publish_api_fail(LOGISTICS_OFFLINE_RPC,$failparams,$failresult);
            }
        
        }
        $ret = parent::add_delivery_callback($result);
        return $ret;
    }

}