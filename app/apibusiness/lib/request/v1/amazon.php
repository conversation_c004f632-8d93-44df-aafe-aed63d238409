<?php
/**
* amazon(亚马逊)接口请求实现
*
* @category apibusiness
* @package apibusiness/lib/request/v1
* <AUTHOR>
* @version $Id: amazon.php 2013-13-12 14:44Z
*/
class apibusiness_request_v1_amazon extends apibusiness_request_partyabstract
{
    /**
     * 发货处理
     *
     * @return void
     * <AUTHOR>
    public function delivery_request($delivery)
    {
        if($delivery['order']['self_delivery'] == 'false') return true;

        return parent::delivery_request($delivery);
    }

    /**
     * 获取发货参数
     *
     * @param Array $delivery 发货单信息
     * @return Array
     * <AUTHOR>
    protected function getDeliveryParam($delivery)
    {

        $item_list = array();

        foreach ($delivery['delivery_items'] as $k=>$v) {
            $item_list[$k]['oid'] = $delivery['order']['order_bn'];
            $item_list[$k]['itemId'] = $v['shop_goods_id'];//取order_items上的商品ID
            $item_list[$k]['num'] = $v['number'];
        }
        
        $param = array(
            'tid'               => $delivery['order']['order_bn'],
            'company_code'      => $delivery['dly_corp']['type'],
            'company_name' => $delivery['logi_name'] ? $delivery['logi_name'] : '',
            'logistics_no'      => $delivery['logi_no'] ? $delivery['logi_no'] : '',
            'item_list'         => json_encode($item_list),//发货明细
        );

        return $param;
    }// TODO TEST

    /**
     * 获取必要的发货数据
     *
     * @param Array $delivery 发货单信息
     * @return MIX
     * <AUTHOR>
    protected function format_delivery($delivery)
    {
        $delivery = parent::format_delivery($delivery);

        $order_id      = $delivery['order']['order_id'];

        $orderObjModel = app::get(self::_APP_NAME)->model('order_objects');
        $orderItemModel = app::get(self::_APP_NAME)->model('order_items');
        $orderObjList = $orderObjModel->getList('*',array('order_id'=>$order_id,'delete'=>'false'));

        //拆单的不管回写第一单还是最后单，只回写发货单上的内容和数量
        $deliItemModel = app::get(self::_APP_NAME)->model('delivery_items');
        $deliItemDModel = app::get(self::_APP_NAME)->model('delivery_items_detail');
        $delivery_items = $deliItemModel->getList('item_id as delivery_item_id,delivery_id,number',array('delivery_id'=>$delivery['delivery_id']));
        foreach($delivery_items as $key=>$item){
            $deliItemDInfo = $deliItemDModel->getList('*',array('delivery_item_id'=>$item['delivery_item_id'], 'delivery_id'=>$item['delivery_id'], 'order_id'=>$order_id), 0, 1);
            //不是捆绑的obj和item数量是相同的，名称货号取obj 数量以发货单明细发货数量为主
            if($deliItemDInfo[0]['item_type'] != 'pkg'){
                foreach ($orderObjList as $obj) {
                    if($obj['obj_id'] == $deliItemDInfo[0]['order_obj_id']){
                        $delivery_items[] = array(
                            'number' => $item['number'],
                            'name' => trim($obj['name']),
                            'bn' => trim($obj['bn']),
                            'shop_goods_id' => $obj['shop_goods_id'],
                        );
                    }
                }
            }else{
                //如果是捆绑的，根据原来obj item对应比例乘以已发的算出obj层已发的数量
                $orderItemInfo = $orderItemModel->getList('*',array('obj_id'=>$deliItemDInfo[0]['order_obj_id'], 'item_id'=>$item['order_item_id'], 'order_id'=>$order_id), 0, 1);
                foreach ($orderObjList as $obj) {
                    if($obj['obj_id'] == $deliItemDInfo[0]['order_obj_id']){
                        $delivery_items[] = array(
                            'number' => $obj['quantity']/$orderItemInfo[0]['nums']*$item['number'],
                            'name' => trim($obj['name']),
                            'bn' => trim($obj['bn']),
                            'shop_goods_id' => $obj['shop_goods_id'],
                        );
                    }
                }
            }
        }

        // 过滤发货单明细中的空格
        foreach((array)$delivery_items as $key=>$item){
            $delivery_items[$key] = array_map('trim', $item);
        }

        $delivery['delivery_items'] = $delivery_items;

        return $delivery;
    }
    /**
     * 售后请求
     * @param   array    $returninfo    售后信息
     * @return  
     * @access  protected
     * <AUTHOR>
    protected function update_aftersale_request($returninfo)
    {

    }

    public function update_order_shippinginfo($order)
    {
        
    }
}