<?php
/**
 * author: jintao
 */
class apibusiness_request_v1_mengdian extends apibusiness_request_partyabstract
{
    #获取发货参数
    protected function getDeliveryParam($delivery){

        $param = array(
            'tid'               => $delivery['order']['order_bn'],
            'company_code'      => $delivery['dly_corp']['type'],
            'company_name'      => $delivery['dly_corp']['name'],
            'logistics_no'      => $delivery['logi_no'] ? $delivery['logi_no'] : '',
        );

        $a_order = app::get('ome')->model('orders')->dump(array('order_bn'=>$delivery['order']['order_bn']));
        $shop_detail = app::get('ome')->model('shop')->dump(array('shop_id'=>$a_order['shop_id']));
        $tmp_area = explode(':', $shop_detail['area']);
        $sender_address = str_replace('/', ' ', $tmp_area[1]);

        $param['sender_address'] = $sender_address ? $sender_address : '';
        $param['sender_name']    = $shop_detail['default_sender'];
        $param['sender_tel']     = $shop_detail['mobile'] ? $shop_detail['mobile'] : $shop_detail['tel'];

        return $param;
    }
}