
# 微信小店 (wxshipin) 集成技术方案

## 1. 需求背景

为了支持公司业务扩展，需要在现有OMS系统中集成“微信小店” (`wxshipin`) 作为一个新的店铺类型。核心要求是，所有来自微信小店的订单，其工厂（`werks`）字段必须自动设置为 `S200`，并确保此规则在订单处理、库存、财务等全流程中保持一致。

## 2. 核心实现方案

为了实现微信小店（`wxshipin`）订单的工厂代码（`werks`）固定为 `S200`，需要修改订单及WMS发货事件的核心逻辑。

**经 `S700TIME` 交叉验证，发现除了标准发货流程外，WMS 的一个自动化任务中也存在独立的 `werks` 赋值逻辑，必须一并修改。**

### 2.1. 修改逻辑

核心逻辑是在系统为发货单分配 `werks` 值之前，增加一个前置判断：
1.  根据订单号（`order_id`）查询订单的店铺类型（`shop_type`）。
2.  如果 `shop_type` 为 `wxshipin`，则将 `werks` 强制赋值为 `S200`。
3.  如果不是，则保持现有的根据时间判断的逻辑（`S700` 或 `5000`）。

### 2.2. 具体代码改动

#### a) 标准发货流程

**目标文件**: `custom/ome/lib/event/receive/delivery.php`
**目标方法**: `_consign()`

此方法内包含了合并发货和单个发货两种场景，都需要修改。

*   **合并发货场景 (`if ($this->__isBind)`)**:
    ```php
    // ...
    $frst_info_for_werks = $orderObj->dump(array('order_id' => $ord_id), 'shop_type');
    if ($frst_info_for_werks['shop_type'] == 'wxshipin') {
        $childly['werks'] = 'S200';
    } else {
        $childly['werks'] = $this->__formatParams['delivery_time'] > S700TIME ? 'S700' : '5000';
    }
    // ...
    ```

*   **单个发货场景 (`else`)**:
    ```php
    // ...
    $frst_info_for_werks = $orderObj->dump(array('order_id' => $ord_id), 'shop_type');
    if ($frst_info_for_werks['shop_type'] == 'wxshipin') {
        $singledly['werks'] = 'S200';
    } else {
        $singledly['werks'] = $this->__formatParams['delivery_time'] > S700TIME ? 'S700' : '5000';
    }
    // ...
    ```

#### b) WMS 自动化发货任务

**目标文件**: `custom/wms/lib/autotask/task/chdelivery.php`

此文件中的逻辑也需要进行相同的修改。虽然无法直接读取文件，但根据代码结构推断，修改方式应与 `delivery.php` 中的单个发货场景类似。

*   **推断修改点**:
    ```php
    // custom/wms/lib/autotask/task/chdelivery.php

    // ... 找到 werks 赋值的地方 ...
    
    // +++ 建议修改 +++
    // (需要根据该文件的具体上下文获取 order_id)
    $orderObj = $this->app->model('ome/orders');
    $frst_info_for_werks = $orderObj->dump(array('order_id' => $order_id), 'shop_type'); // 假设 $order_id 已被定义
    
    if ($frst_info_for_werks['shop_type'] == 'wxshipin') {
        $singledly['werks'] = 'S200';
    } else {
        $singledly['werks'] = $currentTime > S700TIME ? 'S700' : '5000'; // $currentTime 是此文件中的变量
    }
    // --- 赋值逻辑修改结束 ---
    
    // ...
    ```

**重要**: 开发人员在修改时，需确认 `chdelivery.php` 文件中获取 `order_id` 的正确变量名，并应用以上逻辑。遗漏此文件的修改将导致 WMS 自动任务场景下的 `werks` 赋值错误。

## 3. 数据库层面分析

经过对 `custom/ome/dbschema/orders.php`、`custom/ome/dbschema/delivery.php` 等核心表的 `dbschema` 文件分析，确认所有 `werks` 字段均为 `varchar(50)` 或 `varchar(100)` 类型，无数据库级别的默认值、枚举或外键约束。

**结论**：数据库层面不限制写入 `S200` 值，无需进行任何数据库表结构变更。

## 4. 影响范围评估

本次修改虽然核心代码改动不大，但 `werks` 字段影响广泛，需要评估所有下游模块。

*   **订单模块 (`app/ome`, `custom/ome`)**:
    *   **场景**: 订单创建、发货、退换货。
    *   **影响**: 正向影响。`werks` 将被正确设置为 `S200`。退货流程 (`custom/ome/model/reship.php`) 会自动继承发货单的 `werks`，无需修改。

*   **财务模块 (`app/finance`, `custom/finance`)**:
    *   **场景**: 生成SAP凭证、财务报表。
    *   **影响**: **高风险**。存在大量 `if ($werks == 'S700')` 或类似的硬编码逻辑。
    *   **建议**: 必须全面审查财务模块，将所有 `werks` 的判断逻辑从 `== 'S700'` 修改为 `in_array($werks, ['S700', 'S200'])` 或根据财务要求对 `S200` 添加独立处理分支。

### 4.1. 财务模块 (`custom/finance`) 逻辑深入分析

对 `custom/finance` 目录的专项搜索确认了该模块存在多处针对 `S700` 的硬编码逻辑，这些逻辑直接影响 SAP 对接的准确性。

**核心问题**: 在进行任何代码修改前，必须由 **财务部门和开发团队** 共同明确：**从财务和SAP记账角度，`S200` (微信小店) 应该被视为与 `S700` 完全一致的实体，还是一个需要独立配置（如独立成本中心、库位等）的全新实体？**

**具体发现的逻辑点**:

1.  **SAP 成本中心选择**:
    *   **文件**: `custom/finance/lib/sap/common.php`
    *   **逻辑**: `($werks == 'S700') ? $shop_info['kostl17_S700'] : $shop_info['kostl17']`
    *   **说明**: 代码明确指出，当工厂为 `S700` 时，会使用一套特定的成本中心字段 (`kostl17_S700`, `kostl27_S700` 等)。
    *   **待确认**: `S200` 订单是否也应使用这套 `_S700` 后缀的成本中心？还是有自己独立的成本中心？

2.  **SAP 库位选择**:
    *   **文件**: `custom/finance/controller/crontab/delivery.php`, `reship.php`
    *   **逻辑**: `IF(t3.werks='S700', t6.lgort_S700, t6.lgort)`
    *   **说明**: 在生成财务数据时，`S700` 工厂的订单会从一个特殊的 `lgort_S700` 字段获取库位。
    *   **待确认**: `S200` 订单是否也应使用此特殊库位？

3.  **数据订正逻辑**:
    *   **文件**: `custom/finance/controller/crontab/delivery.php`
    *   **逻辑**: 存在直接 `UPDATE sdb_ome_orders SET werks = 'S700'` 的硬编码。
    *   **待确认**: 需要理解此脚本的业务目的，确保 `S200` 的订单不会被错误地更新为 `S700`。

**修改建议**:

*   **如果 `S200` 与 `S700` 财务逻辑一致**:
    *   将所有 `($werks == 'S700')` 的判断修改为 `(in_array($werks, ['S700', 'S200']))`。
    *   这是最简单、影响最小的修改方式。

*   **如果 `S200` 需要独立逻辑**:
    *   必须在现有 `if` 判断中增加 `elseif ($werks == 'S200')` 分支。
    *   需要财务部门提供 `S200` 对应的成本中心、库位等信息，并在代码中进行硬编码或添加到相关配置表中。
    *   这将是更复杂的修改，需要财务和开发的紧密配合。

*   **发票模块 (`app/invoice`, `custom/invoice`)**:
    *   **场景**: 开具电子发票。
    *   **影响**: **高风险**。`custom/invoice/lib/request/piaoyitong/v4/orderprocess.php` 中存在 `in_array($invoiceInfo['werks'],['S700','5000'])` 的判断。
    *   **建议**: 必须将 `S200` 添加到允许的 `werks` 数组中，否则微信小店订单的发票将无法正常开具。

*   **WMS/库存模块 (`app/wms`, `custom/wms`)**:
    *   **场景**: 库存同步、出入库、自动化任务。
    *   **影响**: **中风险**。风险点已识别并纳入修改方案。
    *   **核心风险点**:
        *   `custom/wms/lib/autotask/task/chdelivery.php`: 此文件包含独立的 `werks` 赋值逻辑，是本次修改的**关键遗漏点**。已在 `2.2. b)` 章节中补充了修改方案。
    *   **其他分析**:
        *   `custom/wms/lib/branch/sap/sync/sapbranch.php`: 此文件包含 `SELECT ... 'S700' werks` 的硬编码。经分析，这是一个用于同步特定 SAP 仓库库存同步，固定为 `S700` 的逻辑。


## 4. 测试验证点

1.  **核心功能测试**:
    *   创建 `wxshipin` 店铺类型的订单，在发货后，验证 `sdb_ome_orders` 和 `sdb_ome_delivery` 表中的 `werks` 字段是否为 `S200`。
    *   创建其他店铺类型的订单，验证 `werks` 字段是否依然遵循旧逻辑（`S700` 或 `5000`）。

2.  **全流程测试**:
    *   跟踪一笔 `wxshipin` 订单，完成发货、生成财务凭证、开具发票、完成退货的全过程，确保每个环节 `werks` 均为 `S200` 且处理正确。
    *   回归测试现有主要店铺（如淘宝、京东）的订单全流程，确保不受影响。

3.  **报表测试**:
    *   检查所有与 `werks` 相关的财务、销售报表，确认 `S200` 的数据能被正确统计和展示。

## 5. 预估工时

考虑到该修改影响范围较广，需要充分的回归测试。

*   **开发 (含单元测试)**: 4-6 小时
*   **联调测试 (与财务、发票等模块)**: 8-12 小时
*   **回归测试**: 16 小时
*   **部署与观察**: 2 小时

**总计: 30-36 小时 (约 4-5 人日)**

## 6. 附录：SAP 入账工厂与库位核心逻辑

根据系统代码分析，SAP 的入账工厂和入库库位主要与仓库（`branch`）的配置相关。

### 6.1. SAP 入账工厂 (werks)

*   **定义**: SAP 的入账工厂代码（`werks`）是在 **仓库管理** 模块中为每个仓库（`branch`）独立配置的。
*   **数据来源**: 该值主要来源于 `sdb_wms_branch` 表中与每个仓库记录对应的 `sap_factory_code` 类似字段。
*   **使用**: 在订单处理、库存转移和财务记账等流程中，系统会根据操作所涉及的仓库，获取其配置的 `werks` 值。对于微信小店这类特殊业务，则会通过代码逻辑覆盖为指定的 `S200`。

### 6.2. SAP 入库库位 (lgort)

*   **定义**: SAP 的入库库位（`lgort`）同样在 **仓库管理** 模块中与每个仓库绑定。与工厂不同的是，系统为一个仓库预设了多种库位，以应对不同的业务场景。
*   **数据来源**: 在 `sdb_ome_branch` 表的结构中，定义了多个与库位相关的字段，例如：
    *   `sap_lgort`: SAP 默认入账库位
    *   `sap_defective_lgort`: SAP 不良品入账库位
    *   `sap_s700_lgort`: SAP S700 业务入账库位
    *   `sap_s700_defective_lgort`: SAP S700 业务不良品入账库位
*   **选择逻辑**: 系统在执行具体的入库操作（尤其是在处理退货 `reship` 等财务流程中）时，会根据业务单据的属性动态选择使用哪个库位。
    *   **示例**: 在 `custom/finance/controller/crontab/reship.php` 的退货处理逻辑中，系统会检查商品是否为正品（`is_normal` 标志）。
        *   如果为 **正品**，则会从 `branch` 配置中选取 **默认入账库位**。
        *   如果为 **不良品**，则会选取 **不良品入账库位**。
    *   这种机制确保了不同状态的货物能够被准确地记录到 SAP 系统中对应的虚拟库位，便于后续的库存管理和财务核算。

