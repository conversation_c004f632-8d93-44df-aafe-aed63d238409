# 发票退款数据查询运维工具

## 功能说明

此运维工具用于查询发票退款相关的数据，主要功能包括：

1. 查询发票表中的红票信息
2. 关联查询操作日志中的退款单号
3. 导出CSV格式的数据文件
4. 支持按订单号筛选查询

## 文件位置

- 运维工具类：`custom/console/lib/repair/queryInvoiceRefundData.php`
- 控制器方法：`custom/console/controller/admin/repair.php` 中的 `queryInvoiceRefundData()` 方法
- 界面模板：`custom/console/view/admin/repair.html`

## 使用方法

### 1. 通过Web界面使用

1. 登录OMS系统
2. 进入"系统" -> "IT控制台" -> "控制台入口"
3. 找到"查询发票退款数据"工具
4. 在文本框中输入订单号（支持换行或逗号分隔）
5. 留空则查询所有符合条件的记录
6. 点击"查询"按钮执行查询
7. 查询成功后，点击"下载CSV"按钮下载数据文件

### 2. 通过命令行测试

```bash
cd /path/to/oms/libang
php script/test_invoice_repair.php
```

## 参数说明

- `order_bns`: 订单号参数
  - 类型：字符串
  - 格式：支持多种分隔方式
    - 换行分隔：每行一个订单号
    - 逗号分隔：ORDER001,ORDER002,ORDER003
    - 混合使用：可以同时使用换行和逗号
  - 示例：
    ```
    ORDER001
    ORDER002,ORDER003
    ORDER004
    ```
  - 可选：留空则查询所有符合条件的记录

## 返回结果

工具执行成功后会返回JSON格式的结果：

```json
{
    "code": 200,
    "msg": "{\"count\": 10, \"filename\": \"invoice_refund_data_20241201_143022.csv\", \"csv_data\": \"订单号,备注,红票号,开红时间,退款单号\\n...\", \"data\": [...]}"
}
```

### 返回字段说明

- `count`: 查询到的记录数量
- `filename`: 建议的CSV文件名
- `csv_data`: CSV格式的数据内容（Base64编码）
- `data`: 前10条数据的预览（数组格式）

## 数据字段说明

导出的CSV文件包含以下字段：

1. **订单号**: 原始订单编号
2. **备注**: 操作日志中的备注信息
3. **红票号**: 红字发票号码
4. **开红时间**: 红票开具时间
5. **退款单号**: 关联的退款单号

## 查询逻辑

1. 首先查询 `sdb_invoice_orderinvoice` 表获取发票信息
2. 根据发票ID查询 `sdb_ome_operation_log` 表中的操作日志
3. 筛选包含"仅退款修改发票为待作废：退款单号:"的日志记录
4. 使用正则表达式提取退款单号
5. 整合数据并导出为CSV文件

## 注意事项

1. 查询结果会直接返回CSV数据，不生成服务器文件
2. CSV数据使用UTF-8编码，包含BOM头以确保中文正确显示
3. 时间字段会自动格式化为 `Y-m-d H:i:s` 格式
4. 如果查询结果为空，会返回相应的错误信息
5. 支持多种订单号输入格式：换行分隔、逗号分隔或混合使用
6. 查询成功后会在页面显示数据预览和下载按钮

## 错误处理

- 如果数据库连接失败，会返回错误信息
- 如果查询结果为空，会返回"没有找到符合条件的记录"
- 如果CSV数据生成失败，会抛出异常

## 扩展功能

如需添加更多查询条件或字段，可以修改 `queryInvoiceWithRefundData()` 方法中的SQL查询语句。 