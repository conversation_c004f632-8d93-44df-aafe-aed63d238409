<?php
class console_repair_queryInvoiceRefundData extends console_repair_common{

    public function exec(){
        // 获取传入的订单号参数
        $orderBns = isset($_POST['order_bns']) ? $_POST['order_bns'] : '';
        
        // 检查是否是下载请求
        $download = isset($_POST['download']) && $_POST['download'] == '1';
        
        // 处理订单号参数，支持换行和逗号分隔
        $orderBnsArray = array();
        if (!empty($orderBns)) {
            // 先按换行符分割
            $lines = explode("\n", $orderBns);
            foreach ($lines as $line) {
                // 再按逗号分割
                $items = explode(',', $line);
                foreach ($items as $item) {
                    $item = trim($item);
                    if (!empty($item)) {
                        $orderBnsArray[] = $item;
                    }
                }
            }
        }
        
        try {
            // 查询数据
            $result = $this->queryInvoiceWithRefundData($orderBnsArray);
            
            if (empty($result)) {
                return $this->response('fail', '没有找到符合条件的记录');
            }
            
            // 如果是下载请求，直接输出文件
            if ($download) {
                $this->downloadCSV($result);
                return; // 直接输出，不返回JSON
            }
            
            // 生成CSV数据
            $csvData = $this->generateCSVData($result);
            
            return $this->response('success', json_encode([
                'count' => count($result),
                'csv_data' => $csvData,
                'filename' => 'invoice_refund_data_' . date('Ymd_His') . '.csv',
                'data' => array_slice($result, 0, 10) // 返回前10条数据作为预览
            ]));
            
        } catch (Exception $e) {
            return $this->response('fail', '执行出错: ' . $e->getMessage());
        }
    }
    
    /**
     * 查询发票表和操作日志关联数据
     */
    private function queryInvoiceWithRefundData($orderBns = array()) {
        $db = kernel::database();
        $result = array();
        
        // 先查询发票表
        $where = "1=1";
        if (!empty($orderBns)) {
            $orderBns_str = "'" . implode("','", $orderBns) . "'";
            $where .= " AND order_bn IN ($orderBns_str)";
        }
        
        $invoice_sql = "SELECT 
                            id,
                            order_bn,
                            remarks,
                            invoice_no_red,
                            red_dateline,
                            create_time
                        FROM sdb_invoice_orderinvoice 
                        WHERE $where
                        ORDER BY create_time DESC";
        
        $invoices = $db->select($invoice_sql);
        
        if (empty($invoices)) {
            return $result;
        }
        
        // 循环查询每个发票对应的操作日志
        foreach ($invoices as $invoice) {
            
            $log_sql = "SELECT memo 
                        FROM sdb_ome_operation_log 
                        WHERE obj_id = '{$invoice['id']}' 
                            AND obj_type = 'orderinvoice@invoice'
                            AND memo LIKE '%仅退款修改发票为待作废：退款单号:%'";
            
            $logs = $db->select($log_sql);
            
            if (!empty($logs)) {
                // 提取退款单号
                $refund_no = '';
                $remarks = '';
                foreach ($logs as $log) {
                    if (preg_match('/退款单号:\{([^}]+)\}/', $log['memo'], $matches)) {
                        $refund_no = $matches[1];
                        $remarks = $log['memo'];
                        break;
                    }
                }
                
                $result[] = array(
                    '订单号' => $invoice['order_bn'],
                    '备注' => $remarks,
                    '红票号' => $invoice['invoice_no_red'],
                    '开红时间' => $invoice['red_dateline'],
                    '退款单号' => $refund_no
                );
            }
        }
        
        return $result;
    }
    
    /**
     * 直接下载CSV文件
     */
    private function downloadCSV($data) {
        if (empty($data)) {
            throw new Exception('没有数据需要导出');
        }
        
        // 设置HTTP头信息
        $filename = 'invoice_refund_data_' . date('Ymd_His') . '.csv';
        header('Content-Type: text/csv; charset=utf-8');
        header('Content-Disposition: attachment; filename=' . $filename);
        header('Cache-Control: no-cache, no-store, must-revalidate');
        header('Pragma: no-cache');
        header('Expires: 0');
        
        // 创建输出流
        $fp = fopen('php://output', 'w');
        
        // 写入UTF-8 BOM，确保中文正确显示
        fwrite($fp, "\xEF\xBB\xBF");
        
        // 写入表头
        $headers = array('订单号', '备注', '红票号', '开红时间', '退款单号');
        fputcsv($fp, $headers);
        
        // 写入数据
        foreach ($data as $row) {
            $csv_row = array(
                $row['订单号'],
                $row['备注'],
                $row['红票号'],
                $row['开红时间'] ? date('Y-m-d H:i:s', $row['开红时间']) : '',
                $row['退款单号']
            );
            fputcsv($fp, $csv_row);
        }
        
        // 关闭输出流
        fclose($fp);
        exit;
    }
    
    /**
     * 生成CSV数据
     */
    private function generateCSVData($data) {
        if (empty($data)) {
            throw new Exception('没有数据需要导出');
        }
        
        // 创建临时文件句柄来生成CSV数据
        $fp = fopen('php://temp', 'w+');
        
        // 写入UTF-8 BOM，确保中文正确显示
        fwrite($fp, "\xEF\xBB\xBF");
        
        // 写入表头
        $headers = array('订单号', '备注', '红票号', '开红时间', '退款单号');
        fputcsv($fp, $headers);
        
        // 写入数据
        foreach ($data as $row) {
            $csv_row = array(
                $row['订单号'],
                $row['备注'],
                $row['红票号'],
                $row['开红时间'] ? date('Y-m-d H:i:s', $row['开红时间']) : '',
                $row['退款单号']
            );
            fputcsv($fp, $csv_row);
        }
        
        // 获取CSV数据
        rewind($fp);
        $csvData = stream_get_contents($fp);
        fclose($fp);
        
        return $csvData;
    }
} 