<style>
    #pair_all {
        width: 100%;
        max-width: 1400px;
        margin: 0 auto;
        display: flex;
        gap: 5px;
        flex-wrap: wrap;
        background-color: #f5f7fa;
        min-height: 90vh;
        padding: 8px;
        box-sizing: border-box;
        justify-content: space-between;
    }

    #pair_left {
        flex: 2;
        min-width: 350px;
        border: 1px solid #ddd;
        border-radius: 8px;
        overflow-y: auto;
        padding: 15px;
        height: calc(90vh - 120px);
        background-color: #ffffff;
        box-shadow: 0 2px 4px rgba(0, 0, 0, 0.1);
    }

    #pair_right {
        flex: 2;
        min-width: 350px;
        padding: 15px;
        border: 1px solid #ddd;
        border-radius: 8px;
        overflow-y: auto;
        height: calc(90vh - 120px);
        display: flex;
        flex-direction: column;
        background-color: #ffffff;
        box-shadow: 0 2px 4px rgba(0, 0, 0, 0.1);
    }

    #pair_right #pair_right_response_json {
        border: 1px solid #9b9b9b;
        width: 100%;
        height: 120px;
        padding: 10px;
        font-family: monospace;
        font-size: 12px;
        border-radius: 4px;
        background-color: #f8f9fa;
        resize: vertical;
    }

    #pair_right #pair_right_response_html {
        border: 1px solid #9b9b9b;
        width: 100%;
        padding: 10px;
        overflow-y: auto;
        max-height: 400px;
        border-radius: 4px;
        background-color: #f8f9fa;
    }

    /* 自定义滚动条样式 */
    #pair_left::-webkit-scrollbar,
    #pair_right::-webkit-scrollbar,
    #pair_right #pair_right_response_html::-webkit-scrollbar {
        width: 8px;
    }

    #pair_left::-webkit-scrollbar-track,
    #pair_right::-webkit-scrollbar-track,
    #pair_right #pair_right_response_html::-webkit-scrollbar-track {
        background: #f1f1f1;
        border-radius: 4px;
    }

    #pair_left::-webkit-scrollbar-thumb,
    #pair_right::-webkit-scrollbar-thumb,
    #pair_right #pair_right_response_html::-webkit-scrollbar-thumb {
        background: #c1c1c1;
        border-radius: 4px;
    }

    #pair_left::-webkit-scrollbar-thumb:hover,
    #pair_right::-webkit-scrollbar-thumb:hover,
    #pair_right #pair_right_response_html::-webkit-scrollbar-thumb:hover {
        background: #a8a8a8;
    }

    #pair_left .pair_item {
        border: 1px solid #ddd;
        margin: 10px 0;
        border-radius: 8px;
        overflow: hidden;
        box-shadow: 0 2px 4px rgba(0, 0, 0, 0.1);
    }

    #pair_left .pair_item:hover {
        box-shadow: 0 4px 8px rgba(0, 0, 0, 0.15);
    }

    #pair_left .pair_item .pair_item_header {
        background: linear-gradient(135deg, #f3f5f6 0%, #ebf0f5 100%);
        padding: 12px 15px;
        cursor: pointer;
        border-bottom: 1px solid #ddd;
        font-weight: bold;
        color: #2c3d50;
        display: flex;
        justify-content: space-between;
        align-items: center;
        transition: background-color 0.3s ease;
        border-radius: 8px 8px 0 0;
    }

    #pair_left .pair_item .pair_item_header:hover {
        background: linear-gradient(135deg, #5880cb 0%, #5880cb 100%);
        color: #ffffff;
    }

    #pair_left .pair_item .pair_item_header .toggle-icon {
        font-size: 14px;
        transition: transform 0.3s ease;
    }

    #pair_left .pair_item .pair_item_header.collapsed .toggle-icon {
        transform: rotate(-90deg);
    }

    #pair_left .pair_item .pair_item_content {
        display: block;
        padding: 20px;
        background-color: #ffffff;
        border-radius: 0 0 8px 8px;
        margin: 0;
    }

    #pair_left .pair_item .pair_item_content.collapsed {
        display: none;
    }

    #pair_left .pair_submit {
        color: #ffffff;
        background: #5880cb;
        padding: 8px 15px;
        border: none;
        border-radius: 4px;
        cursor: pointer;
        font-size: 12px;
        transition: background-color 0.3s ease;
        text-decoration: none;
        display: inline-block;
        margin-top: 5px;
    }

    #pair_left .pair_submit:hover {
        background: #2857b5;
    }

    #pair_left .pair_download {
        color: #ffffff;
        background: #5880cb;
        padding: 8px 15px;
        border: none;
        border-radius: 4px;
        cursor: pointer;
        font-size: 12px;
        text-decoration: none;
        display: inline-block;
        transition: background-color 0.3s ease;
        margin-top: 5px;
    }

    #pair_left .pair_download:hover {
        background: #2857b5;
    }

    #cover_float {
        display: none;
    }

    #cover_float .bottom {
        position: fixed;
        width: 100%;
        height: 100%;
        top: 0;
        left: 0;
        background: #999999;
        opacity: 0.5;
        z-index: 9999;
    }

    #cover_float .top {
        position: fixed;
        width: 100%;
        height: 100%;
        top: 0;
        left: 0;
        color: #ffffff;
        text-align: center;
        font-size: 32px;
        z-index: 99999;
        display: flex;
        align-items: center;
        justify-content: center;
        flex-direction: column;
    }

    #cover_float .top::before {
        content: '';
        width: 40px;
        height: 40px;
        border: 4px solid #ffffff;
        border-top: 4px solid transparent;
        border-radius: 50%;
        animation: spin 1s linear infinite;
        margin-bottom: 20px;
    }

    @keyframes spin {
        0% {
            transform: rotate(0deg);
        }

        100% {
            transform: rotate(360deg);
        }
    }

    #pair_left input[type="text"],
    #pair_left textarea {
        border: 1px solid #ddd;
        padding: 8px;
        border-radius: 4px;
        font-size: 12px;
        transition: border-color 0.3s ease;
        width: 100%;
        box-sizing: border-box;
    }

    #pair_left input[type="text"]:focus,
    #pair_left textarea:focus {
        border-color: #5880cb;
        outline: none;
        box-shadow: 0 0 0 2px rgba(0, 123, 255, 0.25);
    }

    #pair_left select {
        border: 1px solid #ddd;
        padding: 6px;
        border-radius: 4px;
        font-size: 12px;
        transition: border-color 0.3s ease;
    }

    #pair_left select:focus {
        border-color: #5880cb;
        outline: none;
        box-shadow: 0 0 0 2px rgba(0, 123, 255, 0.25);
    }

    #pair_left p {
        margin: 8px 0;
        font-size: 12px;
    }

    /* 响应式优化 */
    @media (max-width: 1200px) {
        #pair_all {
            max-width: 100%;
            padding: 10px;
        }

        #pair_left,
        #pair_right {
            min-width: 300px;
        }
    }

    @media (max-width: 992px) {
        #pair_all {
            flex-direction: column;
            gap: 15px;
        }

        #pair_left,
        #pair_right {
            min-width: 100%;
            height: auto;
            max-height: 500px;
        }
    }

    @media (max-width: 480px) {
        #pair_all {
            padding: 5px;
            gap: 10px;
        }

        #pair_left,
        #pair_right {
            min-width: 100%;
            height: auto;
            max-height: 400px;
            padding: 10px;
        }

        #pair_left .pair_item {
            margin: 8px 0;
        }

        #pair_left .pair_item .pair_item_header {
            padding: 8px 12px;
            font-size: 12px;
        }

        #pair_left .pair_item .pair_item_content {
            padding: 10px;
        }
    }
</style>
<div id="pair_all">
    <h2 style="width: 100%; text-align: center; color: #2c3e50; font-size: 24px; margin-bottom: 20px;">立邦OMS控制台 -
        修复数据脚本集</h2>
    <div id="pair_left">

        <div class="pair_item">
            <div class="pair_item_header" onclick="toggleItem(this)">
                <span>A:修复退货单行项目的单价(price)与发货单的均摊单价(cost_avg)一致</span>
                <span class="toggle-icon">▼</span>
            </div>
            <div class="pair_item_content" data-url="<{$reship_item_url}>">
                <p>退货单号：<input type="text" name="a_reship_bn" value="" /> <span style="color: #ff0000;">不支持多单修复</span>
                </p>
                <p><a href="javascript:;" class="pair_submit" data-sub-type="pair_reship_item">提交</a></p>
            </div>
        </div>

        <div class="pair_item">
            <div class="pair_item_header" onclick="toggleItem(this)">
                <span>C:一条龙修复均摊均摊金额和物料标记和包装规格</span>
                <span class="toggle-icon">▼</span>
            </div>
            <div class="pair_item_content" data-url="<{$repairCostAvg_url}>">
                <p><span style="color: #ff0000;">这个脚本是重新计算均摊金额和读取物料配置的逻辑来修复的</span></p>
                <p>订单号：<input type="text" name="c_order_bn" value="" /> <span style="color: #ff0000;">不支持多单修复</span></p>
                <p><a href="javascript:;" class="pair_submit" data-sub-type="pair_cost_avg">提交</a></p>
            </div>
        </div>

        <div class="pair_item">
            <div class="pair_item_header" onclick="toggleItem(this)">
                <span>C:指定物料的金额去修复订单与发货单</span>
                <span class="toggle-icon">▼</span>
            </div>
            <div class="pair_item_content" data-url="<{$specifyRepairCostAvg_url}>">
                <p><span style="color: #ff0000;">指定订单的物料和金额去修改订单 、发货底单、发货通知单的均摊金额和权益金金额</span></p>
                <p>订单号：<input type="text" name="specify_order_bn" value="" /> <span
                        style="color: #ff0000;">不支持多单修复</span></p>
                <p>发货单通知单号：<input type="text" name="specify_wms_dly_bn" value="" /> <span
                        style="color: #ff0000;">不支持多单修复</span></p>
                <p>基础物料编码：<input type="text" name="specify_basic_material_bn" value="" /> <span
                        style="color: #ff0000;">注意多行编码相同不要用这个脚本</span></p>
                <p>对应物料均摊金额：<input type="text" name="specify_material_cost_avg" value="" /></p>
                <p>对应物料均摊权益金：<input type="text" name="specify_material_cost_qyj" value="" /></p>
                <p><a href="javascript:;" class="pair_submit" data-sub-type="pair_cost_avg">提交</a></p>
            </div>
        </div>

        <div class="pair_item">
            <div class="pair_item_header" onclick="toggleItem(this)">
                <span>G:编辑订单-减少物料数量</span>
                <span class="toggle-icon">▼</span>
            </div>
            <div class="pair_item_content" data-url="<{$updateOrderMaterialNumber_url}>">
                <p>订单号：<input type="text" name="g_order_bn" /></p>
                <p>销售编码：<input type="text" name="g_object_bn" /></p>
                <p>减少数量：<input type="text" name="g_less_number" /> <span style="color: #ff0000;">* 减少几个？</span></p>
                <p><a href="javascript:;" class="pair_submit" data-sub-type="pair_invoice_amount">提交</a></p>
            </div>
        </div>

        <div class="pair_item">
            <div class="pair_item_header" onclick="toggleItem(this)">
                <span>S:修复 省份无法选择</span>
                <span class="toggle-icon">▼</span>
            </div>
            <div class="pair_item_content" data-url="<{$provinceRepair_url}>">
                <p>场景：省份无法选择的时候，修复后默认选择订单对应的市级，如果没有市级，默认对应省级下的第一个市,然后自行去修改对应地址</p>
                <p>order_bn：<input type="text" name="province_repair_bn" /> <br /> <span
                        style="color: #ff0000;">提供订单号</span> </p>
                <p><a href="javascript:;" class="pair_submit" data-sub-type="pair_invoice_amount">提交</a></p>
            </div>
        </div>

        <div class="pair_item">
            <div class="pair_item_header" onclick="toggleItem(this)">
                <span>U:修复 权益金导致的货品明细和发货单明细单件均摊价格不一致</span>
                <span class="toggle-icon">▼</span>
            </div>
            <div class="pair_item_content" data-url="<{$costAvgRepair_url}>">
                <p>场景：权益金导致的货品明细和发货单明细单件均摊价格不一致</p>
                <p>delivery_bn：<input type="text" name="costavg_delivery_bn" /> <br /> <span
                        style="color: #ff0000;">提供ome发货单号</span> </p>
                <p><a href="javascript:;" class="pair_submit" data-sub-type="pair_invoice_amount">提交</a></p>
            </div>
        </div>

        <div class="pair_item">
            <div class="pair_item_header" onclick="toggleItem(this)">
                <span>修订订单支付状态</span>
                <span class="toggle-icon">▼</span>
            </div>
            <div class="pair_item_content" data-url="<{$payStatusOrder_url}>">
                <p>订单号：<input type="text" name="payStatusOrderBn" /> <br /> </p>
                <p>选择支付状态:
                    <select name="updatePayStatus" id="logi_id">
                        <option id="STO" value="1">已支付</option>
                        <option id="DBKD" value="4">部分退款</option>
                        <option id="SFBK" value="5">全额退款</option>
                        <option id="EMS" value="6">退款申请中</option>
                        <option id="EMS" value="7">退款中</option>
                    </select>
                </p>
                <br />
                <p>已支付金额：<input type="text" name="updatePayed" /> <br /> </p>
                <p><a href="javascript:;" class="pair_submit" data-sub-type="pair_invoice_amount">提交</a></p>
            </div>
        </div>

        <div class="pair_item">
            <div class="pair_item_header" onclick="toggleItem(this)">
                <span>恢复或者删除订单商品</span>
                <span class="toggle-icon">▼</span>
            </div>
            <div class="pair_item_content" data-url="<{$rebackOrderItems_url}>">
                <p>订单号：<input type="text" name="rebackOrderItemsBn" /> <br /> </p>
                <p>选择操作类型:
                    <select name="rebackOrderItemsType">
                        <option id="reback" value="1">恢复</option>
                        <option id="del" value="2">删除</option>
                    </select>
                </p>
                <p>销售物料编码：<input type="text" name="rebackOrderObjbn" /></p>
                <p>基础物料：<input type="text" name="rebackOrderItems" /> <br />
                    <span style="color: #ff0000;">销售物料不是必填，多个基础物料用;隔开</span>
                </p>
                <p>销售物料商品类型:
                    <select name="rebackOrderObjType">
                        <option value="goods">普通商品</option>
                        <option value="pkg">捆绑商品</option>
                        <option value="gift">赠品</option>
                    </select>
                </p>
                <p><a href="javascript:;" class="pair_submit" data-sub-type="pair_invoice_amount">提交</a></p>
            </div>
        </div>

        <div class="pair_item">
            <div class="pair_item_header" onclick="toggleItem(this)">
                <span>重新拉取订单</span>
                <span class="toggle-icon">▼</span>
            </div>
            <div class="pair_item_content" data-url="<{$newGetOrder_url}>">
                <p>订单号：<input type="text" name="getOrderBn" /> <br /> </p>
                <p>新订单号：<input type="text" name="newGetOrderBn" /> <br /> </p>
                <p><a href="javascript:;" class="pair_submit" data-sub-type="pair_invoice_amount">提交</a></p>
            </div>
        </div>

        <div class="pair_item">
            <div class="pair_item_header" onclick="toggleItem(this)">
                <span>重置订单确认人和确认组</span>
                <span class="toggle-icon">▼</span>
            </div>
            <div class="pair_item_content" data-url="<{$resetOrderGroupIdOrOpId_url}>">
                <p>订单号：<textarea name="resetOrderGroupIdOrderBn"
                        style="width: 350px;height:100px;padding: 5px;"></textarea> <br /> </p>
                <p><a href="javascript:;" class="pair_submit" data-sub-type="pair_invoice_amount">提交</a></p>
            </div>
        </div>

        <div class="pair_item">
            <div class="pair_item_header" onclick="toggleItem(this)">
                <span>H:发货单有，发票没有的补充</span>
                <span class="toggle-icon">▼</span>
            </div>
            <div class="pair_item_content" data-url="<{$createInvoice_url}>">
                <p>wms发货单号：<input type="text" name="ci_delivery_bn" /><br /> <span
                        style="color: #ff0000;">提供订单号，支持英文逗号多单</span></p>
                <p><a href="javascript:;" class="pair_submit" data-sub-type="pair_invoice_amount">提交</a></p>
            </div>
        </div>

        <div class="pair_item">
            <div class="pair_item_header" onclick="toggleItem(this)">
                <span>L:缺少底单详情数据</span>
                <span class="toggle-icon">▼</span>
            </div>
            <div class="pair_item_content" data-url="<{$repairomedeliveryitemsdetail_url}>">
                <p>场景：大仓导出发货单偶尔会缺少物料，原因是ome_delivery_items_detail缺少数据，这个脚本就是insert这个表的数据</p>
                <p>ome_delivery_id：<input type="text" name="odid_id" /></p>
                <p><a href="javascript:;" class="pair_submit" data-sub-type="pair_invoice_amount">提交</a></p>
            </div>
        </div>

        <div class="pair_item">
            <div class="pair_item_header" onclick="toggleItem(this)">
                <span>M:京东面单上传青龙失败，回写发货状态失败</span>
                <span class="toggle-icon">▼</span>
            </div>
            <div class="pair_item_content" data-url="<{$jdtuominqinglongrepair_url}>">
                <p>场景：脱敏完后京东电子面单没有上传到青龙，导致快递员无法发货</p>
                <p>ome_delivery_bn：<input type="text" name="jdqinglong_ome_delivery_bn" /> <br /> <span
                        style="color: #ff0000;">提供底单发货单号，支持英文逗号多单</span> </p>
                <p><a href="javascript:;" class="pair_submit" data-sub-type="pair_invoice_amount">提交</a></p>
            </div>
        </div>

        <div class="pair_item">
            <div class="pair_item_header" onclick="toggleItem(this)">
                <span>O:修复 发货底单存在，通知单不存在的情况</span>
                <span class="toggle-icon">▼</span>
            </div>
            <div class="pair_item_content" data-url="<{$noticemissing_url}>">
                <p>场景：发货底单存在，通知单不存在的情况，会将底单的状态重置为cancel，然后再手动异常订单恢复为初始化状态后再审单</p>
                <p>ome_delivery_bn：<input type="text" name="noticemissing_delivery_bn" /> <br /> <span
                        style="color: #ff0000;">提供底单发货单号，支持英文逗号多单</span> </p>
                <p><a href="javascript:;" class="pair_submit" data-sub-type="pair_invoice_amount">提交</a></p>
            </div>
        </div>

        <div class="pair_item">
            <div class="pair_item_header" onclick="toggleItem(this)">
                <span>P:修复 通知单已发货，底单没发货</span>
                <span class="toggle-icon">▼</span>
            </div>
            <div class="pair_item_content" data-url="<{$shipRepair_url}>">
                <p>场景：通知单已发货，底单没发货，导致不能发货回写和扫单的问题单</p>
                <p>notice_delivery_bn：<input type="text" name="shipRepair_delivery_bn" /> <br /> <span
                        style="color: #ff0000;">提供通知单的发货单号，支持英文逗号多单</span> </p>
                <p><a href="javascript:;" class="pair_submit" data-sub-type="pair_invoice_amount">提交</a></p>
            </div>
        </div>

        <div class="pair_item">
            <div class="pair_item_header" onclick="toggleItem(this)">
                <span>T:修复 越海wms发货回传失败</span>
                <span class="toggle-icon">▼</span>
            </div>
            <div class="pair_item_content" data-url="<{$wmsFailRepair_url}>">
                <p>场景：越海wms发货回传失败修复，常见的有冻结库存失败这些</p>
                <p>delivery_bn：<input type="text" name="oms_delivery_bn" /> <br /> <span
                        style="color: #ff0000;">提供ome发货单号</span> </p>
                <p><a href="javascript:;" class="pair_submit" data-sub-type="pair_invoice_amount">提交</a></p>
            </div>
        </div>

        <div class="pair_item">
            <div class="pair_item_header" onclick="toggleItem(this)">
                <span>X:清空 发货通知单的61单和91单</span>
                <span class="toggle-icon">▼</span>
            </div>
            <div class="pair_item_content" data-url="<{$doEmptyWmsDly6191_url}>">
                <p>发货通知单号：<input type="text" name="do_empty_wms_no" /> <br /> <span
                        style="color: #ff0000;">只能填写一个发货单通知单号</span> </p>
                <p><a href="javascript:;" class="pair_submit" data-sub-type="pair_invoice_amount">提交</a></p>
            </div>
        </div>

        <div class="pair_item">
            <div class="pair_item_header" onclick="toggleItem(this)">
                <span>手动拆单发货</span>
                <span class="toggle-icon">▼</span>
            </div>
            <div class="pair_item_content" data-url="<{$mkDeliveryOrder_url}>">
                <p>订单号：<input type="text" name="mkDeliveryOrderBn" /> <br /> </p>
                <p>选择仓库:
                    <select name="mkDeliveryOrderBnBranch">
                        <option id="cainiao" value="3202">菜鸟家装浙沪绍兴1号仓</option>
                        <option id="yuehai" value="2">越海上海库</option>
                    </select>
                </p>
                <br>
                <p>选择快递公司:
                <div id="c_corpList">
                    <select name="mkDeliveryLogId" id="logi_id">
                        <option id="STO" value="49">申通E物流</option>
                        <option id="DBKD" value="48">德邦快递</option>
                        <option id="SFBK" value="37">上海越海顺丰标快电子面单</option>
                        <option id="EMS" value="3">EMS</option>
                    </select>
                </div>
                </p>
                <p>发货商品：
                    <textarea name="mkDeliveryProduct" style="width: 350px;height:100px;padding: 5px;"></textarea>
                    <br />
                    <span style="color: #ff0000;">发货商品格式: 基础物料编码:数量;基础物料编码:数量</span>
                </p>
                <p><a href="javascript:;" class="pair_submit" data-sub-type="pair_invoice_amount">提交</a></p>
            </div>
        </div>


        <div class="pair_item">
            <div class="pair_item_header" onclick="toggleItem(this)">
                <span>D:设置自动开票状态</span>
                <span class="toggle-icon">▼</span>
            </div>
            <div class="pair_item_content" data-url="<{$resetWmsDeliveryOpenInvoice_url}>">
                <div style="margin-bottom: 10px;">
                    wms_delivery数据表的单号： <span style="color: #ff0000;">填写单号，多单英文逗号隔开</span> <br />
                    <textarea name="wms_origin_no" style="width: 350px;height:100px;padding: 5px;"></textarea>
                    <br />
                    <p>以什么单号做条件：<select name="origin_no_type">
                            <option value="type_no">发货单号</option>
                            <option value="type_no91">91单号</option>
                            <option value="type_no17">17单号</option>
                        </select></p>
                    <br />
                    <p>修改至什么状态：<select name="set_to">
                            <option value="0">未开票</option>
                            <option value="1">已开蓝票</option>
                            <option value="2">开蓝票中</option>
                            <option value="3">开蓝失败</option>
                            <option value="4">已开红票</option>
                            <option value="5">开红票中</option>
                            <option value="6">待作废或红冲</option>
                            <option value="7">红冲失败</option>
                            <option value="8">待再开蓝</option>
                            <option value="98">海外不开票</option>
                            <option value="99">留存不开票</option>
                        </select></p>
                </div>
                <p><a href="javascript:;" class="pair_submit" data-sub-type="pair_open_invoice">提交</a></p>
            </div>
        </div>

        <div class="pair_item">
            <div class="pair_item_header" onclick="toggleItem(this)">
                <span>E:设置发票状态</span>
                <span class="toggle-icon">▼</span>
            </div>
            <div class="pair_item_content" data-url="<{$resetInvoiceStatus_url}>">
                <div style="margin-bottom: 10px;">
                    发票业务单号： <span style="color: #ff0000;">填写发票业务单号，多单英文逗号隔开</span> <br />
                    <textarea name="editInvoiceStatusPosBn" style="width: 350px;height:100px;padding: 5px;"></textarea>
                    <br />
                    <p>修改至什么状态：<select name="editInvoiceStatusSetTo">
                            <option value="none">请选择</option>
                            <option value="0">未开票</option>
                            <option value="1">已开蓝票</option>
                            <option value="3">已红冲</option>
                        </select></p>
                </div>
                <p><a href="javascript:;" class="pair_submit" data-sub-type="pair_invoice_status">提交</a></p>
            </div>
        </div>

        <div class="pair_item">
            <div class="pair_item_header" onclick="toggleItem(this)">
                <span>V:修复 生成一张退货后的新发票</span>
                <span class="toggle-icon">▼</span>
            </div>
            <div class="pair_item_content" data-url="<{$createInvoiceAfterReturn_url}>">
                <p>场景：退货的发票红冲后没有生成新金额的发票数据</p>
                <p>发票列表的业务单号：<input type="text" name="afterReturn_posBn" /> <br /> <span
                        style="color: #ff0000;">提供发票的业务单号</span> </p>
                <p><a href="javascript:;" class="pair_submit" data-sub-type="pair_invoice_amount">提交</a></p>
            </div>
        </div>

        <div class="pair_item">
            <div class="pair_item_header" onclick="toggleItem(this)">
                <span>W:查看 京东订单的权益金分摊明细</span>
                <span class="toggle-icon">▼</span>
            </div>
            <div class="pair_item_content" data-url="<{$jdQuanyijinShareEqually_url}>">
                <p>京东订单号：<input type="text" name="qyj_jdorder_bn" /> <br /> </p>
                <p><a href="javascript:;" class="pair_submit" data-sub-type="pair_invoice_amount">提交</a></p>
            </div>
        </div>

        <div class="pair_item">
            <div class="pair_item_header" onclick="toggleItem(this)">
                <span>Z:订单未生成新发票列表数据修复</span>
                <span class="toggle-icon">▼</span>
            </div>
            <div class="pair_item_content" data-url="<{$createOrderinvoice_url}>">
                <p>订单号：<input type="text" name="createOrderinvoiceOrderBns" /> <br /> </p>
                <p><a href="javascript:;" class="pair_submit" data-sub-type="pair_invoice_amount">提交</a></p>
            </div>
        </div>

        <div class="pair_item">
            <div class="pair_item_header" onclick="toggleItem(this)">
                <span>更新发票税务编码</span>
                <span class="toggle-icon">▼</span>
            </div>
            <div class="pair_item_content" data-url="<{$updateInvoiceSapTaxNumber_url}>">
                <p>订单号：<input type="text" name="createInvoceOrderBn" /> <br /> </p>
                <p>sap_sku或货号：<input type="text" name="sap_sku" /> <br /> </p>
                <p>税务编码：<input type="text" name="sap_tax_number" /> <br /> </p>
                <p><a href="javascript:;" class="pair_submit" data-sub-type="pair_invoice_amount">提交</a></p>
            </div>
        </div>

        <div class="pair_item">
            <div class="pair_item_header" onclick="toggleItem(this)">
                <span>根据订单号下载发票数据</span>
                <span class="toggle-icon">▼</span>
            </div>
            <div class="pair_item_content" data-url="<{$queryInvoiceRefundData_url}>">
                <p>订单号：
                    <textarea name="order_bns" style="height:100px;padding: 5px;"
                        placeholder="多个订单号用换行或逗号分隔，留空查询所有符合条件的记录"></textarea> <br />
                    <span style="color: #ff0000;">根据订单号匹配操作日志关键词"仅退款修改发票为待作废"，下载发票数据</span>
                </p>
                <p>
                    <a href="javascript:;" class="pair_submit" data-sub-type="pair_invoice_amount">查询</a>
                    <a href="javascript:;" class="pair_download" style="display:none;">下载CSV</a></p>
                <p>
            </div>
        </div>

        <div class="pair_item">
            <div class="pair_item_header" onclick="toggleItem(this)">
                <span>B:测试自动审单</span>
                <span class="toggle-icon">▼</span>
            </div>
            <div class="pair_item_content" data-url="<{$autoConfirm_url}>">
                <p>订单号：<input type="text" name="b_order_bn" value="" /> <span
                        style="color: #ff0000;">英文逗号隔开可以多订单号</span></p>
                <p><a href="javascript:;" class="pair_submit" data-sub-type="pair_auto_confirm">提交</a></p>
            </div>
        </div>

        <div class="pair_item">
            <div class="pair_item_header" onclick="toggleItem(this)">
                <span>B:菜鸟订单平台自发货</span>
                <span class="toggle-icon">▼</span>
            </div>
            <div class="pair_item_content" data-url="<{$testCainiaoConfirm_url}>">
                <p>订单号：<input type="text" name="b_order_bn" value="" /> <span
                        style="color: #ff0000;">英文逗号隔开可以多订单号</span></p>
                <p><a href="javascript:;" class="pair_submit" data-sub-type="pair_testCainiaoConfirm">提交</a></p>
            </div>
        </div>

        <div class="pair_item">
            <div class="pair_item_header" onclick="toggleItem(this)">
                <span>I:发起获取京仓库房号</span>
                <span class="toggle-icon">▼</span>
            </div>
            <div class="pair_item_content" data-url="<{$getcanghaikuhao_url}>">
                <p>京仓沧海订单号：<input type="text" name="ch_order_bn" /></p>
                <p><a href="javascript:;" class="pair_submit" data-sub-type="pair_invoice_amount">提交</a></p>
            </div>
        </div>

        <div class="pair_item">
            <div class="pair_item_header" onclick="toggleItem(this)">
                <span>J:刷新订单打入推送case队列</span>
                <span class="toggle-icon">▼</span>
            </div>
            <div class="pair_item_content" data-url="<{$casesystempushorder_url}>">
                <p>刷新订单号：<input type="text" name="brush_order_bn" /> <br /> <span
                        style="color: #ff0000;">提供订单号，支持英文逗号多单</span></p>
                <p><a href="javascript:;" class="pair_submit" data-sub-type="pair_invoice_amount">提交</a></p>
            </div>
        </div>

        <div class="pair_item">
            <div class="pair_item_header" onclick="toggleItem(this)">
                <span>K:发起拼多多手机号解密</span>
                <span class="toggle-icon">▼</span>
            </div>
            <div class="pair_item_content" data-url="<{$pinduoduoshiptel_url}>">
                <p>拼多多订单号：<input type="text" name="pin_order_bn" /></p>
                <p><a href="javascript:;" class="pair_submit" data-sub-type="pair_invoice_amount">提交</a></p>
            </div>
        </div>

        <div class="pair_item">
            <div class="pair_item_header" onclick="toggleItem(this)">
                <span>N:厨卫双包重新推送</span>
                <span class="toggle-icon">▼</span>
            </div>
            <div class="pair_item_content" data-url="<{$repushcwserviceorder_url}>">
                <p>场景：先删除队列列表内的订单，然后重新加入队列，然后再推送</p>
                <p>order_bn：<input type="text" name="repushcwserviceorder_order_bn" /></p>
                <p><a href="javascript:;" class="pair_submit" data-sub-type="pair_invoice_amount">提交</a></p>
            </div>
        </div>

        <div class="pair_item">
            <div class="pair_item_header" onclick="toggleItem(this)">
                <span>sap_2827重新提交一下</span>
                <span class="toggle-icon">▼</span>
            </div>
            <div class="pair_item_content" data-url="<{$get2827Again_url}>">
                <p>2827单号：<textarea name="bn_2827" style="width: 350px;height:100px;padding: 5px;"></textarea> <br />
                </p>
                <p><a href="javascript:;" class="pair_submit" data-sub-type="pair_invoice_amount">提交</a></p>
            </div>
        </div>

        <div class="pair_item">
            <div class="pair_item_header" onclick="toggleItem(this)">
                <span>Q:修复 冻结库存释放失败。（订单级别）</span>
                <span class="toggle-icon">▼</span>
            </div>
            <div class="pair_item_content" data-url="<{$freezefailByorder_url}>">
                <p>场景：审单的时候，审不下去，操作日志里报"订单货品冻结释放失败!</p>
                <p>order_bn：<input type="text" name="freezefail_order_bns" /> <br /> <span
                        style="color: #ff0000;">提供订单号，支持英文逗号多单</span> </p>
                <p><a href="javascript:;" class="pair_submit" data-sub-type="pair_invoice_amount">提交</a></p>
            </div>
        </div>

        <div class="pair_item">
            <div class="pair_item_header" onclick="toggleItem(this)">
                <span>R:修复 冻结库存释放失败。（仓库级别）</span>
                <span class="toggle-icon">▼</span>
            </div>
            <div class="pair_item_content" data-url="<{$freezefailBybranch_url}>">
                <p>场景：发货的时候，发不出去，操作日志里报"订单货品冻结释放失败!</p>
                <p>notice_delivery_bn：<input type="text" name="freezefail_wms_dly_bns" /> <br /> <span
                        style="color: #ff0000;">提供大仓发货通知单号，支持英文逗号多单</span> </p>
                <p><a href="javascript:;" class="pair_submit" data-sub-type="pair_invoice_amount">提交</a></p>
            </div>
        </div>

        <div class="pair_item">
            <div class="pair_item_header" onclick="toggleItem(this)">
                <span>Y:菜鸟仓退货单预占未关联修复</span>
                <span class="toggle-icon">▼</span>
            </div>
            <div class="pair_item_content" data-url="<{$cainiaoReshipAssociation_url}>">
                <p>退货单号：<input type="text" name="cainiaoSalesAfterReshipBn" /> <br /> </p>
                <p><a href="javascript:;" class="pair_submit" data-sub-type="pair_invoice_amount">提交</a></p>
            </div>
        </div>

        <div class="pair_item">
            <div class="pair_item_header" onclick="toggleItem(this)">
                <span>创建退换货单失败数量不对修复</span>
                <span class="toggle-icon">▼</span>
            </div>
            <div class="pair_item_content" data-url="<{$createReshipNumberError_url}>">
                <p>订单号：<input type="text" name="createReshipOrderBn" /> <br /> </p>
                <p><a href="javascript:;" class="pair_submit" data-sub-type="pair_invoice_amount">提交</a></p>
            </div>
        </div>

        <div class="pair_item">
            <div class="pair_item_header" onclick="toggleItem(this)">
                <span>按bn获取批次号</span>
                <span class="toggle-icon">▼</span>
            </div>
            <div class="pair_item_content" data-url="<{$getBatchcodeByBn_url}>">
                <p>基础物料编码：<textarea name="bn" style="width: 350px;height:100px;padding: 5px;"></textarea> <br /> </p>
                <p><a href="javascript:;" class="pair_submit" data-sub-type="pair_invoice_amount">提交</a></p>
            </div>
        </div>
    </div>
    <div id="pair_right">
        <h3
            style="font-size: 16px; color: #2c3e50; margin: 0 0 10px 0; padding: 8px 0; border-bottom: 2px solid #3498db; font-weight: bold;">
            JSON结果：</h3>
        <div id="pair_right_response">
            <textarea id="pair_right_response_json"></textarea>
        </div>
        <p></p>
        <h3
            style="font-size: 16px; color: #2c3e50; margin: 0 0 10px 0; padding: 8px 0; border-bottom: 2px solid #3498db; font-weight: bold;">
            HTML结果：</h3>
        <div id="pair_right_response_html">

        </div>
    </div>
</div>

<div id="cover_float">
    <div class="top">loading...</div>
    <div class="bottom"></div>
</div>

<script>

    // 展开/折叠功能
    function toggleItem(header) {
        var content = header.nextElementSibling;
        var icon = header.querySelector('.toggle-icon');

        if (content.classList.contains('collapsed')) {
            content.classList.remove('collapsed');
            header.classList.remove('collapsed');
        } else {
            content.classList.add('collapsed');
            header.classList.add('collapsed');
        }
    }

    // 页面加载时默认折叠所有功能项
    window.addEvent('domready', function () {
        var items = $$('.pair_item');
        items.each(function (item) {
            var header = item.querySelector('.pair_item_header');
            var content = item.querySelector('.pair_item_content');
            if (header && content) {
                header.classList.add('collapsed');
                content.classList.add('collapsed');
            }
        });
    });

    $$('.pair_submit').addEvent('click', function () {

        responseEmpty();

        var type = this.get('data-sub-type');
        var contentElement = this.getParent("div");
        var subUrl = contentElement.get("data-url");
        var subDataString = contentElement.toQueryString();
        var taskNameEle = contentElement.getPrevious("div.pair_item_header").querySelector('span').textContent;
        //console.log(subDataString);
        //console.log(subUrl);

        // 组装好请求对象
        var myRequest = new Request({

            url: subUrl,
            method: 'post',
            data: subDataString,
            onRequest: function () {
                //myElement.set('text', 'loading...');
                $("cover_float").setStyle('display', 'block');
            },
            onSuccess: function (responseText) {
                $("cover_float").setStyle('display', 'none');
                responseHandle(responseText, taskNameEle, subUrl);

            },
            onFailure: function () {
                $("cover_float").setStyle('display', 'none');
                alert('请求失败，请检查网络连接或联系管理员');
            }

        });

        // 发送ajax请求
        myRequest.send();

    });


    function responseHandle(responseText, taskName, subUrl) {

        var responseAreaJson = $("pair_right_response_json");
        var responseAreaHtml = $("pair_right_response_html");
        var json = JSON.decode(responseText);
        var html = '';

        // view json
        responseAreaJson.set('text', responseText);

        html += '<h3>' + taskName + '</h3>';
        // view html
        if (json.code == 200) {
            html += '<p style="color: #0bb20c;padding-left: 10px;font-size: 14px;">执行成功!!</p>';

            // 特殊处理发票退款数据查询
            if (taskName.indexOf('根据订单号下载发票数据') !== -1) {
                try {
                    var data = JSON.decode(json.msg);
                    if (data.count && data.csv_data) {
                        // 显示下载按钮
                        var downloadBtn = document.querySelector('.pair_download');
                        if (downloadBtn) {
                            downloadBtn.style.display = 'inline-block';
                            downloadBtn.onclick = function () {
                                // 创建隐藏表单提交下载请求
                                var form = document.createElement('form');
                                form.method = 'POST';
                                form.action = subUrl;
                                form.style.display = 'none';

                                // 添加订单号参数
                                var orderInput = document.createElement('input');
                                orderInput.type = 'hidden';
                                orderInput.name = 'order_bns';
                                var orderTextarea = document.querySelector('textarea[name="order_bns"]');
                                orderInput.value = orderTextarea ? orderTextarea.value : '';
                                form.appendChild(orderInput);

                                // 添加下载标识
                                var downloadInput = document.createElement('input');
                                downloadInput.type = 'hidden';
                                downloadInput.name = 'download';
                                downloadInput.value = '1';
                                form.appendChild(downloadInput);

                                // 提交表单
                                document.body.appendChild(form);
                                form.submit();
                                document.body.removeChild(form);
                            };
                        }

                        // 显示数据预览
                        if (data.data && data.data.length > 0) {
                            html += '<div style="padding-left: 10px;margin-top: 10px;">';
                            html += '<span style="font-size: 14px;font-weight: bold;">数据预览（前10条）：</span><br/>';
                            html += '<table border="1" style="border-collapse: collapse; margin-top: 5px; width: 100%; font-size: 11px;">';
                            html += '<tr style="background-color: #f8f9fa;"><th style="padding: 8px; border: 1px solid #ddd;">订单号</th><th style="padding: 8px; border: 1px solid #ddd;">红票号</th><th style="padding: 8px; border: 1px solid #ddd;">退款单号</th><th style="padding: 8px; border: 1px solid #ddd;">开红时间</th></tr>';
                            data.data.forEach(function (row, index) {
                                var bgColor = index % 2 === 0 ? '#ffffff' : '#f8f9fa';
                                html += '<tr style="background-color: ' + bgColor + ';">';
                                html += '<td style="padding: 6px; border: 1px solid #ddd;">' + (row['订单号'] || '') + '</td>';
                                html += '<td style="padding: 6px; border: 1px solid #ddd;">' + (row['红票号'] || '') + '</td>';
                                html += '<td style="padding: 6px; border: 1px solid #ddd;">' + (row['退款单号'] || '') + '</td>';
                                html += '<td style="padding: 6px; border: 1px solid #ddd;">' + (row['开红时间'] || '') + '</td>';
                                html += '</tr>';
                            });
                            html += '</table>';
                            html += '<p style="margin-top: 5px;">共找到 ' + data.count + ' 条记录</p>';
                            html += '</div>';
                        }
                    }
                } catch (e) {
                    console.log('解析发票退款数据失败:', e);
                    html += '<p style="color: #ff6b35;padding-left: 10px;font-size: 12px;">数据解析失败，请检查返回的数据格式</p>';
                }
            } else {
                html += '<div style="padding-left: 10px;"><span style="font-size: 14px;font-weight: bold;">信息反馈：</span><br/>' + json.msg + '</div>';
            }
        } else if (json.code == 500) {
            html += '<p style="color: #ff0000;padding-left: 10px;font-size: 14px;">执行失败!!</p>';
            html += '<div style="padding-left: 10px;"><span style="font-size: 14px;font-weight: bold;">信息反馈：</span><br/>' + json.msg + '</div>';
        } else {
            html += '<p style="color: #ff6b35;padding-left: 10px;font-size: 14px;">未知错误，请检查返回的数据</p>';
            html += '<div style="padding-left: 10px;"><span style="font-size: 14px;font-weight: bold;">返回信息：</span><br/>' + json.msg + '</div>';
        }

        responseAreaHtml.set('html', html);

    }


    function responseEmpty() {
        $("pair_right_response_json").empty();
        $("pair_right_response_html").empty();
        // 隐藏下载按钮
        var downloadBtn = document.querySelector('.pair_download');
        if (downloadBtn) {
            downloadBtn.style.display = 'none';
        }
        // 清除所有下载按钮的事件监听器
        var downloadBtns = document.querySelectorAll('.pair_download');
        downloadBtns.forEach(function (btn) {
            btn.onclick = null;
        });
    }


</script>
