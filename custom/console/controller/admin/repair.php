<?php
/**
 * Created by PhpStorm.
 * User: shelt<PERSON><PERSON>
 * Date: 2020/6/15
 * Time: 17:38
 * Email: <EMAIL>
 * Comment:
 */

class console_ctl_admin_repair extends desktop_controller{


    public function index(){

        $this->pagedata['reship_item_url'] = "/index.php?app=console&ctl=admin_repair&act=repairReshipItemCostAvg";
        $this->pagedata['autoConfirm_url'] = "/index.php?app=console&ctl=admin_repair&act=autoConfirm";
        $this->pagedata['testCainiaoConfirm_url'] = "/index.php?app=console&ctl=admin_repair&act=testCainiaoConfirm";
        $this->pagedata['repairCostAvg_url'] = "/index.php?app=console&ctl=admin_repair&act=repairCostAvg";
        $this->pagedata['specifyRepairCostAvg_url'] = "/index.php?app=console&ctl=admin_repair&act=specifyRepairCostAvg";
        $this->pagedata['resetWmsDeliveryOpenInvoice_url'] = "/index.php?app=console&ctl=admin_repair&act=resetWmsDeliveryOpenInvoice";
        $this->pagedata['resetInvoiceStatus_url'] = "/index.php?app=console&ctl=admin_repair&act=resetInvoiceStatus";
        $this->pagedata['updateOrderMaterialNumber_url'] = "/index.php?app=console&ctl=admin_repair&act=updateOrderMaterialNumber";
        $this->pagedata['createInvoice_url'] = "/index.php?app=console&ctl=admin_repair&act=createInvoice";
        $this->pagedata['getcanghaikuhao_url'] = "/index.php?app=console&ctl=admin_repair&act=getcanghaikuhao";
        $this->pagedata['casesystempushorder_url'] = "/index.php?app=console&ctl=admin_repair&act=pushcaseorder";
        $this->pagedata['pinduoduoshiptel_url'] = "/index.php?app=console&ctl=admin_repair&act=pinduoduoDecrypt";
        $this->pagedata['repairomedeliveryitemsdetail_url'] = "/index.php?app=console&ctl=admin_repair&act=repairomedeliveryitemsdetail";
        $this->pagedata['jdtuominqinglongrepair_url'] = "/index.php?app=console&ctl=admin_repair&act=jdtuominqinglongrepai";
        $this->pagedata['repushcwserviceorder_url'] = "/index.php?app=console&ctl=admin_repair&act=repushcwserviceorder";
        $this->pagedata['noticemissing_url'] = "/index.php?app=console&ctl=admin_repair&act=noticemissing";
        $this->pagedata['shipRepair_url'] = "/index.php?app=console&ctl=admin_repair&act=shipRepair";
        $this->pagedata['freezefailByorder_url'] = "/index.php?app=console&ctl=admin_repair&act=freezefailByOrder";
        $this->pagedata['freezefailBybranch_url'] = "/index.php?app=console&ctl=admin_repair&act=freezefailByBranch";
        $this->pagedata['provinceRepair_url'] = "/index.php?app=console&ctl=admin_repair&act=provinceRepair";
        $this->pagedata['wmsFailRepair_url'] = "/index.php?app=console&ctl=admin_repair&act=wmsFailRepair";
        $this->pagedata['costAvgRepair_url'] = "/index.php?app=console&ctl=admin_repair&act=costAvgRepair";
        $this->pagedata['createInvoiceAfterReturn_url'] = "/index.php?app=console&ctl=admin_repair&act=createInvoiceAfterReturn";
        $this->pagedata['jdQuanyijinShareEqually_url'] = "/index.php?app=console&ctl=admin_repair&act=jdQuanyijinShareEqually";
        $this->pagedata['doEmptyWmsDly6191_url'] = "/index.php?app=console&ctl=admin_repair&act=doEmptyWmsDly6191";
        $this->pagedata['cainiaoReshipAssociation_url'] = "/index.php?app=console&ctl=admin_repair&act=doCainiaoReshipAssociation";
        $this->pagedata['createOrderinvoice_url'] = "/index.php?app=console&ctl=admin_repair&act=createOrderinvoice";
        $this->pagedata['queryInvoiceRefundData_url'] = "/index.php?app=console&ctl=admin_repair&act=queryInvoiceRefundData";
        $this->pagedata['createReshipNumberError_url'] = "/index.php?app=console&ctl=admin_repair&act=createReshipNumberError";
        $this->pagedata['updateInvoiceSapTaxNumber_url'] = "/index.php?app=console&ctl=admin_repair&act=updateInvoiceSapTaxNumber";
        $this->pagedata['get2827Again_url'] = "/index.php?app=console&ctl=admin_repair&act=get2827Again";
        $this->pagedata['resetOrderGroupIdOrOpId_url'] = "/index.php?app=console&ctl=admin_repair&act=resetOrderGroupIdOrOpId";
        $this->pagedata['mkDeliveryOrder_url'] = "/index.php?app=console&ctl=admin_repair&act=mkDeliveryOrder";
        # 恢复删除商品
        $this->pagedata['rebackOrderItems_url'] = "/index.php?app=console&ctl=admin_repair&act=rebackOrderItems";
        $this->pagedata['payStatusOrder_url'] = "/index.php?app=console&ctl=admin_repair&act=payStatusOrder";
        $this->pagedata['newGetOrder_url'] = "/index.php?app=console&ctl=admin_repair&act=newGetOrder";
        # 按bn获取批次号
        $this->pagedata['getBatchcodeByBn_url'] = "/index.php?app=console&ctl=admin_repair&act=getBatchcodeByBn";

        $this->page("admin/repair.html");

    }

    public function newGetOrder() {
        $obj_syncorder = kernel::single("ome_syncorder");
        $orderInfo = app::get('ome')->model('orders')->dump(['order_bn' => $_POST['getOrderBn']], 'shop_id,order_bn,shop_type');

        $shopId = $orderInfo['shop_id'];

        $rsp_data = kernel::single('erpapi_router_request')->set('shop', $orderInfo['shop_id'])->order_get_order_detial(trim($_POST['getOrderBn']));
        $sdf_order = $rsp_data['data']['trade'];

        if ($orderInfo['shop_type'] == '360buy') {
            $sdf_order['store_order'] = '京仓订单';
        } else {
            $sdf_order['store_order'] = '菜鸟订单';
        }

        $sdf_order['order_bn'] = trim($_POST['newGetOrderBn']);
        $sdf_order['tid'] = trim($_POST['newGetOrderBn']);
        if ($obj_syncorder->get_order_log($sdf_order, $shopId, $msg)) {
            echo json_encode(['code'=>200,'msg'=>'操作成功']);
        } else {
            echo json_encode(['code'=>500,'msg'=>$msg]);
        }
    }

    public function payStatusOrder() {
        $order_bn = trim($_POST['payStatusOrderBn']);
        if (empty($order_bn)) {
            echo json_encode(['code'=>500,'msg'=>'订单号必填']);
            exit;
        }

        $orderInfo = app::get('ome')->model('orders')->dump(array('order_bn'=>$order_bn),'order_id,pay_status,payed');
        if (empty($orderInfo)) {
            echo json_encode(['code'=>500,'msg'=>'订单不存在']);
            exit;
        }

        if($_POST['updatePayed'] !== '') {
            $orderInfo['payed'] = $_POST['updatePayed'];
        }

        if ($orderInfo['order_id']) {
            $result = app::get('ome')->model('orders')->update(array('pay_status'=>$_POST['updatePayStatus'], 'payed'=>$orderInfo['payed']),array('order_id'=>$orderInfo['order_id']));
            if ($result) {
                echo json_encode(['code'=>200,'msg'=>'操作成功']);
            } else {
                echo json_encode(['code'=>500,'msg'=>'操作失败']);
            }
        }
    }

    public function rebackOrderItems(){
        $order_bn = trim($_POST['rebackOrderItemsBn']);
        if (empty($order_bn)) {
            echo json_encode(['code'=>500,'msg'=>'订单号必填']);
            exit;
        }

        $orderInfo = app::get('ome')->model('orders')->dump(array('order_bn'=>$order_bn),'order_id');
        if (empty($orderInfo)) {
            echo json_encode(['code'=>500,'msg'=>'订单不存在']);
            exit;
        }

        $items = trim($_POST['rebackOrderItems']);
        $items = explode(';', $items);
        if (empty($items)) {
            echo json_encode(['code'=>500,'msg'=>'请填写商品']);
            exit;
        }

        $obj_bn = trim($_POST['rebackOrderObjbn']);
        if ($obj_bn) {
            $obj_type = trim($_POST['rebackOrderObjType']);
            if ($obj_type) {
                $objInfo = app::get('ome')->model('order_objects')->dump(array('bn'=>$obj_bn,'order_id'=>$orderInfo['order_id'],'obj_type'=>$obj_type));
            } else {
                $objInfo = app::get('ome')->model('order_objects')->dump(array('bn'=>$obj_bn,'order_id'=>$orderInfo['order_id']));
            }
        }

        if ($_POST['rebackOrderItemsType'] == '1') {
            if ($objInfo) {
                $result = app::get('ome')->model('order_items')->update(array('delete'=>0),array('order_id'=>$orderInfo['order_id'], 'bn'=>$items, 'obj_id'=>$objInfo['obj_id']));
            } else {
                $result = app::get('ome')->model('order_items')->update(array('delete'=>0),array('order_id'=>$orderInfo['order_id'], 'bn'=>$items));
            }
            if ($result) {
                $logObj = app::get('ome')->model('operation_log');
                $logObj->write_log('order_edit@ome', $order_id, '手动恢复删除商品:'.$_POST['rebackOrderItems']);
            }
        } else {
            kernel::database()->beginTransaction();
            if ($objInfo) {
                $list = app::get('ome')->model('order_items')->getList('*', array('order_id'=>$orderInfo['order_id'], 'bn'=>$items,'obj_id'=>$objInfo['obj_id']));
            } else {
                $list = app::get('ome')->model('order_items')->getList('*', array('order_id'=>$orderInfo['order_id'], 'bn'=>$items));
            }
            foreach ($list as $key => $value) {
                if ($value['delete'] == 'true') {
                    echo json_encode(['code'=>500,'msg'=>$value['bn'].'已删除不需要重复删除']);
                    exit;
                }
                $affect_row = kernel::database()->exec("UPDATE sdb_ome_order_items set `delete`='true' WHERE order_id={$orderInfo['order_id']} AND item_id={$value['item_id']} AND `delete`='false'");
                if ($affect_row) {
                    kernel::single('material_basic_material_stock_freeze')->unfreeze($value['product_id'], material_basic_material_stock_freeze::__ORDER, 0, $orderInfo['order_id'], '', material_basic_material_stock_freeze::__SHARE_STORE, $value['number']);
                    kernel::single('material_basic_material_stock')->unfreeze($value['product_id'], $value['number']);
                } else {
                    kernel::database()->rollBack();
                    echo json_encode(['code'=>500,'msg'=>$value['bn'].'删除失败']);
                    exit;
                }
            }
            kernel::database()->commit();
            $result = true;
        }

        if ($result) {
            echo json_encode(['code'=>200,'msg'=>'更新成功']);
        } else {
            echo json_encode(['code'=>500,'msg'=>'更新失败']);
        }
    }

    public function mkDeliveryOrder(){

        $order_bn = trim($_POST['mkDeliveryOrderBn']);
        if (empty($order_bn)) {
            echo json_encode(['code'=>500,'msg'=>'订单号必填']);
            exit;
        }

        $combineObj = kernel::single('omeauto_auto_combine');
        $orderInfo = app::get('ome')->model('orders')->dump(array('order_bn'=>$order_bn),'*');
        if (empty($orderInfo)) {
            echo json_encode(['code'=>500,'msg'=>'订单不存在']);
            exit;
        }

        $orders[] = $orderInfo['order_id'];
        $consignee = $orderInfo['consignee'];

        if(empty($_POST['mkDeliveryOrderBnBranch'])){
            echo json_encode(['code'=>500,'msg'=>'请选择仓库']);
            exit;
        }

        if ($_POST['mkDeliveryOrderBnBranch'] == '3202') {
            $consignee['branch_id'] = 3202;
            $logiId = '52';// 菜鸟52
        } else {

            if (empty($_POST['mkDeliveryLogId'])) {
                echo json_encode(['code'=>500,'msg'=>'请选择物流公司']);
                exit;
            }

            $consignee['branch_id'] = $_POST['mkDeliveryOrderBnBranch'];
            $logiId = $_POST['mkDeliveryLogId'];
        }

        $product = $_POST['mkDeliveryProduct'];
        if (empty($product)) {
            echo json_encode(['code'=>500,'msg'=>'请填写发货商品']);
            exit;
        }

        $product = explode(';', $product);

        $splitting_product = array();
        foreach ($product as $key => $value) {
            $object = explode(':', $value);
            // 多个相同商品，指定销售物料
            if (count($object) == 3) {
                $objdata = app::get('ome')->model('order_objects')->dump(array('bn'=>$object[0], 'order_id'=>$orderInfo['order_id']),'obj_id');
                if (empty($objdata)) {
                    echo json_encode(['code'=>500,'msg'=>'填写的发货商品格式有误1'.$object[0]]);
                    exit;
                }
                $item = app::get('ome')->model('order_items')->dump(array('order_id'=>$orderInfo['order_id'], 'obj_id'=>$objdata['obj_id'], 'bn'=>$object[1]),'item_type,product_id,item_id');
                if (empty($item)) {
                    echo json_encode(['code'=>500,'msg'=>'填写的发货商品格式有误2'.$object[1]. json_encode($objdata)]);
                    exit;
                }
                $num = $object[2];
            } else {
                $item = app::get('ome')->model('order_items')->dump(array('order_id'=>$orderInfo['order_id'], 'bn'=>$object[0]),'item_type,product_id,item_id');
                if (empty($item)) {
                    echo json_encode(['code'=>500,'msg'=>'填写的发货商品格式有误3'.$object[0]]);
                    exit;
                }
                $num = $object[1];
            }

            if ($item['item_type'] == 'pkg') {
                $splitting_product[$item['item_type']][$item['product_id']][$item['item_id']] = $num;
            } else {
                $splitting_product[$item['item_type']][$item['product_id']] = $num;
            }
        }

        if (empty($splitting_product)) {
            echo json_encode(['code'=>500,'msg'=>'填写的发货商品格式有误4']);
            exit;
        }

        $result = $combineObj->mkDelivery($orders, $consignee, $logiId, $splitting_product);
        kernel::single('ome_event_trigger_shop_order')->order_message_produce($orders,array('2','3','4'));
        if ($result) {
            echo json_encode(['code'=>200,'msg'=>'发货成功']);
        } else {
            echo json_encode(['code'=>500,'msg'=>'发货失败']);
        }
    }

    public function resetOrderGroupIdOrOpId()
    {
        $order_bn = trim($_POST['resetOrderGroupIdOrderBn']);
        $order = app::get('ome')->model('orders')->dump(array('order_bn' => $order_bn), 'group_id,op_id');
        if ($order) {
            if (empty($order['group_id'])) {
                $order['group_id'] = 0;
            }
            if (empty($order['op_id'])) {
                $order['op_id'] = 0;
            }
            app::get('ome')->model('orders')->update($order, array('order_bn' => $order_bn));
        }
        echo json_encode(['code'=>200,'msg'=>'重置成功']);
    }

    public function get2827Again(){
        $string = trim($_POST['bn_2827']);
        $array = explode(",", $string);
        $msg = "";
        foreach ($array as $value){
            if (substr($value, 0, 1) != "0") {
                $value = "0".$value;
            }
            $mainInfo = app::get('finance')->model('sum_reship')->dump(array("bn_28"=>$value));
            if(empty($mainInfo)){
                $mainInfo = app::get('finance')->model('sum_reship')->dump(array("bn_27"=>$value));
                if(empty($mainInfo)){
                    $msg .= "单号 $value 获取失败:不存在单号；";
                    continue;
                }
            }
            # 根据是否是赠品，请求不同的单号 ------
            if($mainInfo['is_gift'] != '1'){
                // 不是赠品请求28单
                $errorMsg = kernel::single('finance_sap_reship_28')->getSap($mainInfo['id']);
            }else{
                // 是赠品请求27单
                $errorMsg = kernel::single('finance_sap_reship_27')->getSap($mainInfo['id']);
            }

            # 输出报错信息 ------
            if(!$errorMsg){
                $msg .= "单号 $value 获取成功";
            }else{
                $msg .= "单号 $value 获取失败:".$errorMsg;
            }
        }
        echo json_encode(['code'=>200,'msg'=>$msg]);
    }

    public function updateInvoiceSapTaxNumber()
    {
        $order_bn = trim($_POST['createInvoceOrderBn']);
        $orderInfo = app::get('ome')->model('orders')->dump(array('order_bn'=>$order_bn), 'order_id');
        if (empty($orderInfo)) {
            echo json_encode(['code'=>500,'msg'=>'未查询到订单号']);
            exit;
        }
        $order_id = $orderInfo['order_id'];

        $sql = "SELECT d.delivery_bn from sdb_ome_delivery_order o LEFT JOIN sdb_ome_delivery d on o.delivery_id=d.delivery_id where o.order_id=$order_id and d.status='succ'";
        $list = kernel::database()->select($sql);
        if (empty($list)) {
            echo json_encode(['code'=>500,'msg'=>'未查询到发货单号']);
            exit;
        }
        $sap_tax_number = $_POST['sap_tax_number'];
        foreach($list as $row) {
            $delivery_bn = $row['delivery_bn'];
            $sap_sku = trim($_POST['sap_sku']);
            $sql = "select item_id,sap_tax_number,bn,sap_sku,outer_delivery_bn from sdb_wms_delivery_items i LEFT JOIN sdb_wms_delivery d on i.delivery_id=d.delivery_id where outer_delivery_bn='$delivery_bn' and (sap_sku='$sap_sku' or bn='$sap_sku')";
            $itemlist = kernel::database()->select($sql);
            if ($itemlist) {
                foreach($itemlist as $val) {
                    $sql = "update sdb_wms_delivery_items set sap_tax_number='$sap_tax_number' where item_id={$val['item_id']}";
                    kernel::database()->exec($sql);
                }
            }
        }
        echo json_encode(['code'=>200,'msg'=>'处理成功']);
    }

    public function createReshipNumberError()
    {
        $order_bn = $_POST['createReshipOrderBn'];

        $orders = app::get('ome')->model('orders')->dump(array('order_bn'=>$order_bn), 'order_id');
        if (empty($orders)) {
            echo json_encode(['code'=>500,'msg'=>'未查询到订单号']);
            exit;
        }
        $order_id = $orders['order_id'];

        $reshipList = app::get('ome')->model('reship')->getList('reship_id,ome_delivery_bn', array('order_id'=>$order_id,'is_check'=>array('0','2','1','3','11','8')));
        $delivery = array();
        foreach ($reshipList as $key => $value) {
            if (empty($value['ome_delivery_bn'])) {
                continue;
            }

            if(!isset($delivery[$value['ome_delivery_bn']])) {
                $delivery[$value['ome_delivery_bn']] = array();
            }

            $reshipItems = app::get('ome')->model('reship_items')->getList('reship_id,bn,num,ome_dly_item_id', array('reship_id'=>$value['reship_id']));
            if ($reshipItems) {
                foreach ($reshipItems as $k => $v) {
                    $delivery[$value['ome_delivery_bn']][$v['ome_dly_item_id']]['num'] += $v['num'];
                    $delivery[$value['ome_delivery_bn']][$v['ome_dly_item_id']]['bn'] = $v['bn'];
                }
            }
        }

        if($delivery) {
            $sql = "select t1.delivery_id,t1.delivery_bn from sdb_ome_delivery t1 left join sdb_ome_delivery_order t2 on  t1.delivery_id = t2.delivery_id where t1.status='succ' and t2.order_id = $order_id";
            $list =  kernel::database()->select($sql);
            foreach ($list as $key => $value) {
                $delivery_reship = $delivery[$value['delivery_bn']];
                if ($delivery_reship) {
                    foreach($delivery_reship as $ome_dly_item_id => $v) {
                        $num = $v['num'];
                        $bn =  $v['bn'];
                        $sql = "UPDATE sdb_ome_delivery_items  SET reshipping_num = $num WHERE bn='$bn' and item_id=$ome_dly_item_id and delivery_id = {$value['delivery_id']}";
                        kernel::database()->exec($sql);
                    }
                } else {
                    $sql = "UPDATE sdb_ome_delivery_items  SET reshipping_num = 0 WHERE delivery_id = {$value['delivery_id']}";
                    kernel::database()->exec($sql);
                }
            }
            echo json_encode(['code'=>200,'msg'=>'处理成功']);
        } else {
            $sql = "select t1.delivery_id from sdb_ome_delivery t1 left join sdb_ome_delivery_order t2 on  t1.delivery_id = t2.delivery_id where t1.status='succ' and t2.order_id = $order_id";
            $list =  kernel::database()->select($sql);
            if ($list) {
                foreach ($list as $key => $value) {
                    $sql = "UPDATE sdb_ome_delivery_items  SET reshipping_num = 0 WHERE delivery_id = {$value['delivery_id']}";
                    kernel::database()->exec($sql);
                }
            }
            echo json_encode(['code'=>200,'msg'=>'处理成功']);
        }
    }

    public function getBatchcodeByBn(){
        $string = trim($_POST['bn']);
        $array = explode(",", $string);
        if(count($array)>1){
            echo json_encode(['code'=>251,'msg'=>"暂不支持提交多个"]);
            exit;
        }
        $msg = "";
        foreach ($array as $value){

            $sql = "select batch_code from sdb_ome_reship_batchcode where bn in('$value')";
            $info = kernel::database()->selectrow($sql);
            $batch_code = $info['batch_code'];
        }
        echo json_encode(['code'=>200,'msg'=>$batch_code]);
    }

    public function freezefailByBranch(){

        echo kernel::single('console_repair_freezefail')->branchExec();

    }

    /**
     * 订单审核发货单的时候，库存冻结释放失败的场景的数据修复
     */
    public function freezefailByOrder(){

        echo kernel::single('console_repair_freezefail')->orderExec();

    }

    public function shipRepair(){

        echo kernel::single('console_repair_shiprepair')->exec();

    }

    /**
     * 自动审单
     */
    public function autoConfirm(){

        echo kernel::single('console_repair_autoconfirm')->exec();
    }


    /**
     * 测试菜鸟审单
     */
    public function testCainiaoConfirm(){

        $orderBn = $_POST['b_order_bn'];
        $rs = app::get('ome')->model('orders')->dump(array('order_bn' => $orderBn), 'order_id');
        $orderId = $rs['order_id'];
        $errMsg = '';

        if (!$orderId) {
            echo json_encode(['code'=>500,'msg'=>'订单不存在']);
            exit;
        }

        $sql = "update sdb_ome_orders set pause='false',lb_order_cate='31',warehouse_no='NGB2001',is_slow='0' where order_id=".$orderId;
        kernel::database()->exec($sql);

        //执行审核
        $lib = kernel::single('ome_cainiao');
        $result = $lib->combine($orderId,$errMsg);
        if (!$result) {
            echo json_encode(['code'=>500,'msg'=>$errMsg ?: '审核失败']);
            exit;
        }

        // 生成发货底单
        $delivery_order = app::get('ome')->model('delivery_order')->getlist('delivery_id', array('order_id' => $orderId));
        if ($delivery_order) {
            $deliveryids = array_column($delivery_order, 'delivery_id');
            $deliveryInfo = app::get('ome')->model('delivery')->dump(array('delivery_id' => $deliveryids, 'status'=>'ready'), '*');
            if ($deliveryInfo) {
                // 发货
                //
                $time = time();
                $tmp_data = array( 'delivery_bn' => $deliveryInfo['delivery_bn'], 'logi_no' => $deliveryInfo['delivery_bn'], 'status' => 'delivery');
                kernel::single('ome_event_receive_delivery')->update($tmp_data);

                kernel::single('wms_autotask_task_yhcreate')->createWmsDly($deliveryInfo, $msg);

                $deliveryInfo = app::get('ome')->model('delivery')->dump(array('delivery_bn' => $deliveryInfo['delivery_bn']), '*');
                $werks = $deliveryInfo['werks'];
                $sql = "update sdb_wms_delivery set delivery_time=$time,werks='$werks' where outer_delivery_bn ='" . $deliveryInfo['delivery_bn'] . "'";
                kernel::database()->exec($sql);

                echo json_encode(['code'=>200,'msg'=>'发货成功']);
            } else {
                echo json_encode(['code'=>500,'msg'=>'发货失败，未生成发货单1']);
            }
        } else {
            echo json_encode(['code'=>500,'msg'=>'发货失败，未生成发货单']);
        }
    }


    /**
     * 修复均摊金额
     */
    public function repairCostAvg(){

        echo kernel::single('console_repair_repaircostavg')->exec();

    }

    /**
     * 重置wms发货单数据表的自动开票开关
     */
    public function resetWmsDeliveryOpenInvoice(){

        echo kernel::single('console_repair_resetWmsDeliveryOpenInvoice')->exec();

    }


    /**
     * 重置发票的状态
     * 操作时要提交原因过来，并且记录到发票的操作日志
     */
    public function resetInvoiceStatus(){

        echo kernel::single('console_repair_editInvoiceStatus')->exec();
        // $this->resp('fail','施工中...敬请期待...');

    }


    /**
     * 修复退货单行项目的单价金额要与发货单的均摊单价金额一致
     */
    public function repairReshipItemCostAvg(){

        echo kernel::single('console_repair_repairReshipItemCostAvg')->exec();

    }


    /**
     * 修改订单物料数量
     */
    public function updateOrderMaterialNumber(){

        echo kernel::single('console_repair_updateOrderMaterialNumber')->exec();

    }


    /**
     * 有发货单，但是没有发票的情况 补发票数据
     */
    public function createInvoice(){

        echo kernel::single('console_repair_createInvoice')->exec();

    }


    /**
     * 沧海订单查询
     */
    public function getcanghaikuhao(){

        echo kernel::single('console_repair_getcanghaikuhao')->exec();

    }


    /**
     * 推送case系统订单调试的地方
     */
    public function pushcaseorder()
    {

        echo kernel::single('console_repair_pushcaseorder')->exec();

    }


    /**
     * 拼多多解密调试
     */
    public function pinduoduoDecrypt(){

        echo kernel::single('console_repair_pinduoduoDecrypt')->exec();

    }


    /**
     * 发货单底单缺少 delivery_item_detail 表数据的修复
     */
    public function repairomedeliveryitemsdetail(){

        echo kernel::single('console_repair_repairomedeliveryitemsdetail')->exec();

    }

    /**
     * 京东订单 青龙系统未上传的修复脚本
     */
    public function jdtuominqinglongrepai(){

        echo kernel::single('console_repair_jdtuominqinglongrepai')->exec();

    }


    /**
     * 厨卫双包推送 删除队列数据从新组装数据到队列后的修复脚本
     */
    public function repushcwserviceorder(){

        echo kernel::single('console_repair_repushcwserviceorder')->exec();

    }

    /**
     * 底单存在，通知单丢失修复脚本
     */
    public function noticemissing(){

        echo kernel::single('console_repair_noticemissing')->exec();

    }


    /**
     * 修改发票数据的开票金额
     */
    public function updateInvoiceAmount(){
        $this->resp('fail','施工中...敬请期待...');
    }

    /**
     * 省份修复
     */
    public function provinceRepair(){
        echo kernel::single('console_repair_provinceRepair')->exec();
    }

    /**
     * wms 发货回传失败修复
     */
    public function wmsFailRepair(){
        echo kernel::single('console_repair_wmsFailRepair')->exec();
    }

    public function costAvgRepair(){
        echo kernel::single('console_repair_costAvgRepair')->exec();
    }

    /**
     * 返回数据方法
     * @param $status
     * @param string $msg
     */
    protected function resp($status,$msg='执行成功'){

        if($status == 'success'){
            echo json_encode(['code'=>200,'msg'=>$msg]);
        }else{
            echo json_encode(['code'=>500,'msg'=>$msg]);
        }

        exit();

    }


    /**
     * 退货的发票红冲后没有生成新金额的发票数据
     * @return void
     */
    public function createInvoiceAfterReturn(){
        echo kernel::single('console_repair_createInvoiceAfterReturn')->exec();
    }


    /**
     * 京东权益金分摊明细
     */
    public function jdQuanyijinShareEqually(){

        echo kernel::single('console_repair_jdQuanyijinShareEqually')->exec();

    }

    /**
     * 指定订单的物料，和指定金额去修改订单 、发货底单、发货通知单的均摊金额和权益金金额
     */
    public function specifyRepairCostAvg(){
        echo kernel::single('console_repair_specifyRepairCostAvg')->exec();
    }


    /**
     * 清空发货单通知单的6191单号
     */
    public function doEmptyWmsDly6191(){
        echo kernel::single('console_repair_doEmptyWmsDly6191')->exec();
    }



    public function doCainiaoReshipAssociation(){
        echo kernel::single('console_repair_doCainiaoReshipAssociation')->exec();
    }

    public function createOrderinvoice(){
        echo kernel::single('console_repair_createOrderinvoice')->exec();
    }

    /**
     * 查询发票退款数据
     */
    public function queryInvoiceRefundData(){
        echo kernel::single('console_repair_queryInvoiceRefundData')->exec();
    }


}
