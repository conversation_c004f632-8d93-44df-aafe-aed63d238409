<?php

class console_ctl_admin_delivery extends desktop_controller
{

    var $name = "发货单列表";
    var $workground = "console_center";


    /**
     *
     * 发货单列表
     */
    function index()
    {

        $user = kernel::single('desktop_user');

        $actions = array();

        $base_filter = array(
            'status' => array('ready', 'progress', 'succ'),
            'type' => 'normal',
            'pause' => 'false',
            'disabled' => 'false',
        );
        $base_filter = array_merge($base_filter, $_GET);
        switch ($_GET['view']) {
            case '1':
                #$actions[] =
                #    array('label' => '发送至第三方',
                #        'submit' => 'index.php?app=console&ctl=admin_delivery&act=batch_sync',
                #        'confirm' => '你确定要对勾选的发货单发送至第三方吗？',
                #        'target' => 'refresh');
                $actions[] = array(
                    'label' => '发送至第三方',
                    'submit' => $this->url.'&act=batchSyncWms',
                    'target'=>'dialog::{width:600,height:200,title:\'批量对勾选的发货单发送至WMS仓储\'}"',
                );
                $actions[] = array('label' => 'OMS强制取消', 'submit' => 'index.php?app=console&ctl=admin_delivery&act=batch_cancel', 'confirm' => "强制撤销前，请一定先行确认：该订单在菜鸟仓查无此单！\n\n警告：本操作将会直接取消oms发货单并释放库存，并不可恢复，请谨慎使用！！！", 'target' => 'refresh');
                break;
        }
        if ($user->has_permission('console_process_receipts_print_export')) {
            $base_filter_str = http_build_query($base_filter);
            if ($_GET['view'] == '1') {
                $query_status = 'progress';
            } elseif ($_GET['view'] == '2') {
                $query_status = 'succ';
            }
            $actions[] = array(
                'label' => '导出',
                'submit' => 'index.php?app=omedlyexport&ctl=ome_delivery&act=index&action=export&status=' . $query_status,
                'target' => 'dialog::{width:600,height:300,title:\'导出\'}'
            );
        }
        $params = array(
            'use_buildin_new_dialog' => false,
            'use_buildin_set_tag' => false,
            'use_buildin_recycle' => false,
            'use_buildin_import' => false,
            'use_buildin_export' => false,
            'use_buildin_filter' => true,
            'use_view_tab' => true,
            'actions' => $actions,
            'title' => '发货单',
            'base_filter' => $base_filter,
            'orderBy'=>'delivery_id DESC'
        );


        $this->finder('console_mdl_delivery', $params);
    }

    //未发货 已发货 全部
    function _views()
    {
        $oDelivery = app::get('ome')->model('delivery');
        $base_filter = array(
            'type' => 'normal',
            'pause' => 'false',
            'parent_id' => 0,
            'disabled' => 'false',
        );
        $sub_menu = array(
            0 => array('label' => app::get('base')->_('全部'), 'filter' => array('status' => array('ready', 'progress', 'succ')), 'optional' => false),
            1 => array('label' => app::get('base')->_('待发货'), 'filter' => array('process' => array('FALSE'), 'status' => array('progress', 'ready')), 'optional' => false),
            2 => array('label' => app::get('base')->_('已发货'), 'filter' => array('process' => array('TRUE'), 'status' => 'succ'), 'optional' => false),
            3 => array('label' => app::get('base')->_('待取件'), 'filter' => array('logi_status' => array('0'), 'process' => array('TRUE'), 'status' => 'succ'), 'optional' => false),
            4 => array('label' => app::get('base')->_('已揽收'), 'filter' => array('logi_status' => array('1'), 'process' => array('TRUE'), 'status' => 'succ'), 'optional' => false),
            5 => array('label' => app::get('base')->_('已签收'), 'filter' => array('logi_status' => array('3'), 'process' => array('TRUE'), 'status' => 'succ'), 'optional' => false),

        );

        if(isset($_GET['view'])){
            $viewNumber = $_GET['view'];
        }else{
            $viewNumber = 0;
        }

        foreach ($sub_menu as $k => $v) {
            if (!IS_NULL($v['filter'])) {
                $v['filter'] = array_merge($v['filter'], $base_filter);
            }

            $sub_menu[$k]['filter'] = $v['filter'] ? $v['filter'] : null;
            if($viewNumber == $k){
                $sub_menu[$k]['addon'] = $oDelivery->viewcount($v['filter']);
            }else{
                $sub_menu[$k]['addon'] = 'showtab';
            }

            $sub_menu[$k]['href'] = 'index.php?app=console&ctl=' . $_GET['ctl'] . '&act=' . $_GET['act'] . '&view=' . $i++;
        }

        return $sub_menu;
    }


    /**
     * 发送至第三方
     * @
     * @
     * @access  public
     * <AUTHOR>
     */
    function batch_sync()
    {
        $this->begin('');
        kernel::database()->exec('commit');

        $ids = $_POST['delivery_id'];
        if (!empty($ids)) {
            foreach ($ids as $deliveryid) {
                $delivery = app::get('ome')->model('delivery')->dump(array('delivery_id' => $deliveryid, 'stock_status' => 'false', 'deliv_status' => 'false', 'expre_status' => 'false', 'pause' => 'false', 'process' => 'false', 'status' => array('progress', 'ready')), 'delivery_id');

                if ($delivery) {
                    ome_delivery_notice::create($deliveryid);
                }
            }
        }

        $this->end(true, '命令已经被成功发送！！');
    }

    /**
     * 批量推送发货单至WMS仓储
     */
    public function batchSyncWms()
    {
        @ini_set('memory_limit','512M');
        set_time_limit(0);

        $deliveryObj = app::get('ome')->model('delivery');

        $deliveryIds = $_POST['delivery_id'];

        //check
        if($_POST['isSelectedAll'] == '_ALL_'){
            die('不能使用全选功能,每次最多选择500条!');
        }

        if(empty($deliveryIds)){
            die('请选择需要操作的发货单!');
        }

        if(count($deliveryIds) > 500){
            die('每次最多只能选择500条!');
        }

        $this->pagedata['GroupList'] = json_encode($deliveryIds);

        $this->pagedata['request_url'] = $this->url .'&act=ajaxSyncWms';

        //调用desktop公用进度条(第4个参数是增量传offset,否则默认一直为0)
        parent::dialog_batch('ome_mdl_delivery', false, 50, 'incr');
    }


    public function ajaxSyncWms()
    {
        $deliveryObj = app::get('ome')->model('delivery');

        $branchLib = kernel::single('ome_branch');

        $retArr = array(
                'itotal' => 0,
                'isucc' => 0,
                'ifail' => 0,
                'err_msg' => array(),
        );

        //获取发货单号
        $postdata = json_decode(base64_decode($_POST['primary_id']), true);
        if(!$postdata){
            echo 'Error: 请先选择发货单';
            exit;
        }

        //filter
        $filter = $postdata['f'];
        $offset = intval($postdata['f']['offset']);
        $limit = intval($postdata['f']['limit']);

        if(empty($filter)){
            echo 'Error: 没有找到查询条件';
            exit;
        }

        //data
        $dataList = $deliveryObj->getList('delivery_id,delivery_bn,branch_id,sync_status', $filter, $offset, $limit);

        //check
        if(empty($dataList)){
            echo 'Error: 没有获取到发货单';
            exit;
        }

        //count
        $retArr['itotal'] = count($dataList);

        //获取京东一件代发的仓库列表
        $wms_type = 'yjdf';
        $error_msg = '';

        $dlyExtObj = app::get('console')->model('delivery_extension');
        //list
        foreach ($dataList as $key => $val)
        {
            $delivery_id = $val['delivery_id'];
            $branch_id = $val['branch_id'];

            $dlyExtInfo = $dlyExtObj->dump(array('delivery_bn'=>$val['delivery_bn']),'original_delivery_bn');
            //已经有第三方订单号,不允许重复推送
            if($dlyExtInfo['original_delivery_bn']){
                //error
                $retArr['ifail'] += 1;
                $retArr['err_msg'][] = $val['delivery_bn'].'已经有第三方单号,不允许重复推送';

                continue;
            }

            //推送失败的发货单,检查订单是否已经申请退款
            #if($val['sync_status'] == '2'){
                //@todo：发货单未同步第三方仓储，并且申请退款的订单直接取消发货单
                $sql = "SELECT b.order_id, b.process_status, b.status, b.pay_status FROM `sdb_ome_delivery_order` AS a
                        LEFT JOIN sdb_ome_orders AS b ON a.order_id=b.order_id WHERE a.delivery_id=". $delivery_id;
                $orderInfo = $deliveryObj->db->selectrow($sql);
                if(in_array($orderInfo['pay_status'], array('5', '6', '7'))){
                    //error
                    $retArr['ifail'] += 1;
                    $retArr['err_msg'][] = $val['delivery_bn'].'已经申请退款或已退款,请检查!';

                    continue;
                }
            #}

            //request
            ome_delivery_notice::create($delivery_id);

            //succ
            $retArr['isucc'] += 1;
        }

        echo json_encode($retArr),'ok.';
        exit;
    }



    /**
     * 撤销订单
     * @param
     * @return
     * @access  public
     * <AUTHOR>
     */
    function pauseorder()
    {
        $is_super = kernel::single('desktop_user')->is_super();
        if ($is_super) {

            $this->page('admin/pauseorder.html');
        } else {
            echo '非管理员不可操作';
        }
    }

    function back()
    {
        $this->begin();
        $is_super = kernel::single('desktop_user')->is_super();
        if (!$is_super) {
            $this->end(false, '非超级管理员不可操作');
        }
        if (empty($_POST['select_bn']) && empty($_POST['bn_select'])) {
            $this->end(false, '请输入正确的单号', '', $autohide);
        }
        $autohide = array('autohide' => 3000);
        $Objdly = app::get('ome')->model('delivery');
        $OiObj = app::get('ome')->model('delivery_items');
        $ObjdlyOrder = app::get('ome')->model('delivery_order');

        if ($_POST['select_bn'] == 'order_bn') {
            $select_type = 'order_bn';
            $detail = $Objdly->getDeliveryByOrderBn($_POST['bn_select']);
            if (!$detail) {
                $this->end(false, '发货单未生成 不走此流程', '', $autohide);
            }
            $detail['consignee']['name'] = $detail['ship_name'];
            $detail['consignee']['area'] = $detail['ship_area'];
            $detail['consignee']['province'] = $detail['ship_province'];
            $detail['consignee']['city'] = $detail['ship_name'];
            $detail['consignee']['district'] = $detail['ship_district'];
            $detail['consignee']['addr'] = $detail['ship_addr'];
            $detail['consignee']['zip'] = $detail['ship_zip'];
            $detail['consignee']['telephone'] = $detail['ship_telephone'];
            $detail['consignee']['mobile'] = $detail['ship_mobile'];
            $detail['consignee']['email'] = $detail['ship_email'];
            $detail['consignee']['r_time'] = $detail['ship_name'];
        }
        $items = $OiObj->getList('*', array('delivery_id' => $detail['delivery_id']));
        if (empty($detail)) {
            $this->end(false, '没有该单号的发货单', '', $autohide);
        }
        if ($detail['status'] == 'back') {
            $this->end(false, '该发货单已经被打回，无法继续操作', '', $autohide);
        }
        #获取订单
        $order_bn = $ObjdlyOrder->getOrderInfo('order_bn', $detail['delivery_id']);
        if ($detail['status'] == 'cancel') {
            $this->end(false, '该发货单已经被取消，无法继续操作' . "<br>" . '订单号:' . $order_bn[0]['order_bn'], '', $autohide);
        }
        if ($detail['delivery_logi_number'] > 0) {
            $this->end(false, '该发货单已部分发货，无法继续操作', '', $autohide);
        }
        if ($detail['pause'] == 'true') {
            $this->end(false, '该发货单已暂停，无法继续操作', '', $autohide);
        }
        if ($detail['process'] == 'true') {
            $this->end(false, '该发货单已经发货，无法继续操作', '', $autohide);
        }

        $basicMaterialLib = kernel::single('material_basic_material');

        foreach ($items as $k => $value) {
            $barcode = $basicMaterialLib->getBasicMaterialCode($value['product_id']);
            $items[$k]['barcode'] = $barcode;
        }

        if (($detail['stock_status'] == 'true') || ($detail['deliv_status'] == 'true') || ($detail['expre_status'] == 'true')) {
            $this->pagedata['is_confirm'] = true;
        }
        if ($detail['is_bind'] == 'true') {
            $countinfo = $Objdly->getList('count(parent_id)', array('parent_id' => $detail['delivery_id']));
            $count = $countinfo[0]['count(parent_id)'];
            $this->pagedata['height'] = 372 + 26 * $count;
        }
        $this->pagedata['select_type'] = $select_type;
        $this->pagedata['bn_select'] = $_POST['bn_select'];
        $this->pagedata['items'] = $items;
        $this->pagedata['detail'] = $detail;
        $this->page('admin/pauseorder.html');
    }

    /**
     * 打回操作
     *
     */
    function doReback()
    {
        $autohide = array('autohide' => 3000);
        $this->begin('index.php?app=ome&ctl=admin_delivery&showmemo&p[0]=' . $_POST['id']);
        $is_super = kernel::single('desktop_user')->is_super();
        if (!$is_super) {
            $this->end(false, '非超级管理员不可操作');
        }
        if (empty($_POST['id']) && !empty($_POST['flag'])) {
            $this->end(false, '请选择至少一张发货单', '', $autohide);
        }
        if (empty($_POST['memo'])) {
            $this->end(false, '备注请不要留空', '', $autohide);
        }
        $delivery_id = $_POST['delivery_id'];
        $dlyObj = app::get('ome')->model("delivery");
        $orderObj = app::get('ome')->model("orders");
        $oOperation_log = app::get('ome')->model('operation_log');

        $deliveryInfo = $dlyObj->dump($delivery_id, '*', array('delivery_items' => array('*')));
        $tmpdly = array(
            'delivery_id' => $deliveryInfo['delivery_id'],
            'status' => 'cancel',
            'logi_id' => '0',
            'logi_name' => '',
            'logi_no' => NULL,
        );
        $dlyObj->save($tmpdly);
        $oOperation_log->write_log('delivery_modify@ome', $deliveryInfo['delivery_id'], '发货单撤销');

        //库存管控
        $storeManageLib = kernel::single('ome_store_manage');
        $storeManageLib->loadBranch(array('branch_id' => $deliveryInfo['branch_id']));

        $order_ids = $dlyObj->getOrderIdByDeliveryId($deliveryInfo['delivery_id']);
        //是否是合并发货单
        if ($deliveryInfo['is_bind'] == 'true') {
            //取关联发货单号进行暂停
            $delivery_ids = $dlyObj->getItemsByParentId($deliveryInfo['delivery_id'], 'array');
            if ($delivery_ids) {
                foreach ($delivery_ids as $id) {
                    $tmpdly = array(
                        'delivery_id' => $id,
                        'status' => 'cancel',
                        'logi_id' => '0',
                        'logi_name' => '',
                        'logi_no' => NULL,
                    );
                    $dlyObj->save($tmpdly);
                    $oOperation_log->write_log('delivery_modify@ome', $id, '发货单撤销');

                    $delivery = $dlyObj->dump($i, 'delivery_id,branch_id,shop_id', array('delivery_items' => array('*'), 'delivery_order' => array('*')));

                    $de = $delivery['delivery_order'];
                    $or = array_shift($de);
                    $ord_id = $or['order_id'];

                    //仓库库存处理
                    $params['params'] = array_merge($delivery, array('order_id' => $ord_id));
                    $params['node_type'] = 'cancelDly';
                    $processResult = $storeManageLib->processBranchStore($params, $err_msg);
                }
            }

            //取关联订单号进行还原

            if ($order_ids) {
                foreach ($order_ids as $id) {
                    $order['order_id'] = $id;
                    $order['confirm'] = 'N';
                    $order['process_status'] = 'unconfirmed';
                    $orderObj->save($order);
                    $oOperation_log->write_log('order_modify@ome', $id, '发货单撤销,订单还原需重新审核,备注:' . $_POST['memo']);
                }
            }
        } else {
            //还原当前订单
            $order_id = $order_ids[0];
            $order['order_id'] = $order_id;
            $order['confirm'] = 'N';
            $order['process_status'] = 'unconfirmed';

            $orderObj->save($order);
            $oOperation_log->write_log('order_modify@ome', $order_id, '发货单撤销,订单还原需重新审核,备注:' . $_POST['memo']);

            //仓库库存处理
            $params['params'] = array_merge($deliveryInfo, array('order_id' => $order_id));
            $params['node_type'] = 'cancelDly';
            $processResult = $storeManageLib->processBranchStore($params, $err_msg);
        }
        //冻结库存释放
        $this->end(true, '操作成功', 'index.php?app=console&ctl=admin_delivery&act=pauseorder', $autohide);
    }

    /**
     * 填写打回备注
     *
     * @param bigint $dly_id
     */
    function showmemo($dly_id)
    {
        $deliveryObj = app::get('ome')->model("delivery");
        $dly = $deliveryObj->dump($dly_id, 'is_bind,delivery_bn');

        if ($dly['is_bind'] == 'true') {
            $ids = $deliveryObj->getItemsByParentId($dly_id, 'array');
            $returnids = implode(',', $ids);
            $idd = array();
            if ($ids) {
                foreach ($ids as $v) {
                    $delivery = $deliveryObj->dump($v, 'delivery_bn');
                    $order_id = $deliveryObj->getOrderBnbyDeliveryId($v);
                    $idd[$v]['delivery_bn'] = $delivery['delivery_bn'];
                    $idd[$v]['order_bn'] = $order_id['order_bn'];
                    $idd[$v]['delivery_id'] = $v;
                }
            }
            $this->pagedata['returnids'] = $returnids;
            $this->pagedata['ids'] = $ids;
            $this->pagedata['idd'] = $idd;
        }
        $this->pagedata['delivery_id'] = $dly_id;
        $this->pagedata['delivery_bn'] = $dly['delivery_bn'];
        $this->display("admin/delivery_showmemo.html");
    }

    /**
     * 强制撤销第三方仓储发货单
     *
     * return
     */
    function batch_cancel()
    {
        $this->begin('');
        kernel::database()->exec('commit');
        $ids = $_REQUEST['delivery_id'];
        $branchLib = kernel::single('ome_branch');
        $oOperation_log = app::get('ome')->model('operation_log');
        $deliveryObj = app::get('ome')->model('delivery');
        if (!empty($ids)) {
            //排除自有仓
            $branchIds = $branchLib->getBranchListBywms();

            $branchIds = $branchIds ? implode(',', $branchIds) : '';
            $delivery_list = kernel::database()->select("SELECT delivery_id,branch_id,delivery_bn from sdb_ome_delivery where delivery_id in (" . join(',', $ids) . ") AND status in('ready','progress') AND branch_id not in(" . $branchIds . ")");

            foreach ((array)$delivery_list as $delivery) {
                $data = array(
                    'status' => 'cancel',
                    'memo' => '发货单请求第三方仓储取消失败,强制取消!',
                    'delivery_bn' => $delivery['delivery_bn'],
                );
                kernel::single('ome_event_receive_delivery')->update($data);
            }
        }
        $this->end(true, '命令已经被成功发送！！');
    }

    /**
     * 加密字段显示明文
     *
     * @return void
     * <AUTHOR>
    public function showSensitiveData($delivery_id, $fieldType)
    {
        // if (!kernel::single('desktop_user')->has_permission('sensitive_data_show')) {
        //     $this->splash('error',null,'您无权查看该数据');
        // }

        $deliveryMdl = app::get('console')->model('delivery');

        $delivery = $deliveryMdl->db_dump($delivery_id, 'shop_id,shop_type,ship_name,ship_tel,ship_mobile,ship_addr,delivery_id,delivery_bn,member_id,ship_province,ship_city,ship_district,memo');

        if ($delivery['member_id']) {
            $member = app::get('ome')->model('members')->db_dump($delivery['member_id'], 'uname');

            $delivery['uname'] = $member['uname'];
        }


        $order_ids = $deliveryMdl->getOrderIdByDeliveryId($delivery['delivery_id']);

        $order = app::get('ome')->model('orders')->db_dump(array('order_id' => $order_ids), 'order_bn');
        $delivery['order_bn'] = $order['order_bn'];

        // 处理加密
        $delivery['encrypt_body'] = kernel::single('ome_security_router', $delivery['shop_type'])->get_encrypt_body($delivery, 'delivery', $fieldType);

        // 推送日志
        // kernel::single('base_hchsafe')->order_log(array('operation'=>'查看发货单收货人信息','tradeIds'=>array($delivery['delivery_bn'])));


        $this->splash('success', null, null, 'redirect', $delivery);
    }

    /**
     * 发货单回传列表
     */
    public function delivery_callback_list(){

        $actions[] = array('label' => '重新回传',
            'submit' => 'index.php?app=console&ctl=admin_delivery&act=delivery_callback',
            'target' => 'refresh');

        $params = array(
            'use_buildin_new_dialog' => false,
            'use_buildin_set_tag' => false,
            'use_buildin_recycle' => false,
            'use_buildin_import' => false,
            'use_buildin_export' => false,
            'actions' => $actions,
            'title' => '发货单回传列表',
            'orderBy'=>'log_id DESC'
        );


        $this->finder('ome_mdl_shipment_status_log', $params);
    }

    /**
     * 发货单重新回传操作方法
     */
    function delivery_callback() {

        $this->begin('');
        kernel::database()->exec('commit');
        $ids = $_REQUEST['log_id'];

        $shipment_status_log_obj = app::get("ome")->model("shipment_status_log");
        $shipment_status_logs = $shipment_status_log_obj->getList('order_bn',['log_id'=>$ids]);
        $order_bns = array_column($shipment_status_logs,'order_bn');
        $order_ids = app::get("ome")->model("orders")->getList('order_id',['order_bn'=>$order_bns]);
        $order_key_ids = array_column($order_ids,'order_id');

        if (!empty($order_key_ids)) {
            kernel::single('ome_event_trigger_shop_delivery')->delivery_confirm_retry($order_key_ids);
        }
        $this->end(true, '命令已经被成功发送！！');
    }
}
