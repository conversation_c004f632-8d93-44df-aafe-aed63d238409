<?php
/**
 * ShopEx licence
 *
 * @copyright  Copyright (c) 2005-2010 ShopEx Technologies Inc. (http://www.shopex.cn)
 * @license  http://ecos.shopex.cn/ ShopEx License
 */

class ome_ctl_admin_service_shopnotify extends desktop_controller{

    //支持订购到期提醒推送的平台列表
    public $shopTypes = array('taobao','tmall','pinduoduo','360buy');
    
    /**
     * @获取过期消息提醒
     * @access public
     * @param void
     * @return void/json
     */
    public function validity() {

        // 如果当前控制器不是dashboard，则不进行验证
        if ($_GET['ctl'] != 'dashboard') {
            return array();
        }

        $rpcNotifyList = kernel::single('ome_shopnotify')->getShopNotifyList();

        if(count($rpcNotifyList) <= 0) {
            return array();
        }
        
        echo json_encode(array('has_expire'=> 'true'));
    }

    /**
     * @根据服务日期期限弹窗显示的提示信息页
     * @access public
     * @param void
     * @return void
     */
    public function alert() {

        $rpcNotifyList = kernel::single('ome_shopnotify')->getShopNotifyList();

        $this->pagedata['rpcNotifyList'] = $rpcNotifyList;
        
        $this->display('admin/service/shopnotify.html');
    }

}