<?php
$db['order_delay'] = array(
    'columns' =>
        array(
            'id' =>
                array(
                    'type' => 'int unsigned',
                    'required' => true,
                    'pkey' => true,
                    'editable' => false,
                    'extra' => 'auto_increment',
                ),
            'order_id' =>
                array(
                    'type' => 'table:orders@ome',
                    'required' => true,
                    'editable' => false,
                    'in_list' => false,
                    'default_in_list' => false,
                    'label' => '订单号',
                ),
            'delay_time' =>
                array(
                    'type' => 'time',
                    'label' => '延迟时间',
                    'width' => 130,
                    'in_list' => true,
                    'default_in_list' => true,
                    'default' => 0,
                ),
            'process_status' =>
                array(
                    'type' =>
                        array(
                            '0' => '未处理',
                            '1' => '已处理',
                        ),
                    'default' => '0',
                    'required' => true,
                    'label' => '处理状态',
                    'width' => 70,
                    'editable' => false,
                    'filtertype' => 'yes',
                    'filterdefault' => true,
                    'in_list' => true,
                    'default_in_list' => false,
                ),
            'createtime' =>
                array(
                    'type' => 'time',
                    'label' => '创建时间',
                    'width' => 130,
                    'editable' => false,
                    'filtertype' => 'time',
                    'filterdefault' => true,
                    'in_list' => true,
                ),
        ),
    'index' =>
        array(
            'ind_order_id' => array('columns' => array(0 => 'order_id')),
            'ind_createtime' => array('columns' => array(0 => 'createtime')),
        ),
    'engine' => 'innodb',
);
