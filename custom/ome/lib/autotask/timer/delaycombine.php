<?PHP
/**
 * 延迟审单
 */
class ome_autotask_timer_delaycombine
{
    /**
     * @description 订单获取后台处理
     * @access public
     * @param void
     * @return bool
     */
    public function process($params, &$error_msg='')
    {
        if ('running' == cachecore::fetch(__CLASS__)) {
            $error_msg = "执行中";
            return true;
        }
        cachecore::store(__CLASS__, 'running',60);
        $time = time();
        $batchLogModel = app::get('ome')->model('batch_log');
        $count = $batchLogModel->count(array('log_type'=>'ordertaking','source'=>'direct','status'=>array('0','2')));
        if ($count > 0) {
            $error_msg = '存在未执行完成的订单，暂时不处理';
            return true;
        }
        // 查询订单
        $orderDelayMdl    = app::get('ome')->model('order_delay');
        $delayList = $orderDelayMdl->getList('id,order_id', array('delay_time|sthan' => $time, 'process_status'=>'0'));
        if (!$delayList) {
            return true;
        }
        $orderIdWhere = " order_id in ('".implode("','", array_column($delayList, 'order_id'))."')";
        $sql = "select shop_type,order_bool_type,order_id, order_combine_hash, order_combine_idx, pay_status, is_cod, createtime, paytime, shop_id from sdb_ome_orders where pay_status in ('1') and lb_order_cate='0' and op_id is null and group_id is null and abnormal='false' and is_fail='false' and status='active' and lb_fire_phoenix='0' and process_status in ('unconfirmed') and is_slow='0' and {$orderIdWhere} " ;

        $list = kernel::database()->select($sql);
        foreach ($list as $k => $order) {

            // 如果是拼多多风控订单 由于没有收货人信息不审单
            if ($order['shop_type'] == 'pinduoduo' && ($order['order_bool_type'] & ome_order_bool_type::__RISK_CODE) == ome_order_bool_type::__RISK_CODE) {
                unset($list[$k]);
            }
        }

        if ($list) {
            //整合数据, 合成订单组
            $orderGroup = array();
            foreach ($list as $key => $row) {
                $idx = sprintf('%s||%s||%d', $row['order_combine_hash'], $row['order_combine_idx'],$key);
                $orderGroup[$idx]['orders'][$key] = $row['order_id'];
                $orderGroup[$idx]['cnt'] += 1;
            }
            foreach ($orderGroup as $key => $group) {
                $orderGroup[$key]['orders'] = join(',', $group['orders']);
            }
            $orderCnt = 0; $params = array ();
            foreach ($orderGroup as $key=>$group) {
                $orderCnt += $group['cnt'];

                list ($hash, $idx) = explode('||', $key);

                $params[] = array('idx' => $idx, 'hash' => $hash, 'orders' => explode(',', $group['orders']));
            }

            $op = kernel::single('ome_func')->getDesktopUser();
            $batchLog = array(
                'createtime'   => time(),
                'op_id'        => $op['op_id'],
                'op_name'      => $op['op_name'],
                'batch_number' => $orderCnt,
                'succ_number'  => '0',
                'fail_number'  => '0',
                'status'       => '0',
                'log_type'     => 'ordertaking',
                'log_text'     => serialize($params),
            );
            $batchLogModel->save($batchLog);
            foreach (array_chunk($params, 5) as $param) {
                $push_params = array(
                    'data' => array(
                        'orderidx'  => json_encode($param),
                        'log_id'    => $batchLog['log_id'],
                        'task_type' => 'ordertaking'
                    ),
                    'url' => kernel::openapi_url('openapi.autotask','service')
                );
                kernel::single('taskmgr_interface_connecter')->push($push_params);
            }
        }
        $orderDelayMdl->update(array('process_status'=>'1'),array('id'=>array_column($delayList, 'id')));
        cachecore::store(__CLASS__, '',1);

        return  true;
    }
}
