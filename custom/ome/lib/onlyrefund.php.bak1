<?php

/**
 *  仅退款接收后处理
 */

class ome_onlyrefund
{

    /**
     * 仅退款接收后处理
     *
     * @param string $refund_apply_bn 退款申请单号
     * @param string &$msg             错误信息
     * @return boolean
     */
    public function processByRefund($refund_apply_bn, &$msg, $isForce = false)
    {
        $refund_apply_mdl = app::get('ome')->model('refund_apply');
        $refundData = $refund_apply_mdl->dump(array('refund_apply_bn' => $refund_apply_bn));
        if ($refundData['status'] != '4') {
            $msg = '退款申请单未完成，不处理';
            return false;
        }

        if ($refundData['source'] != 'matrix') {
            $msg = '退款单不是平台退款，不处理';
            return false;
        }

        if ($refundData['last_modified'] < strtotime('2024-11-20 00:00:00') && !$isForce)  {
            $msg = '早于2024年11月20日，不处理';
            return false;
        }

        // 如果是退货退款，则不处理
        if ($refundData['refund_type'] != '1') {
            $msg = '退款单不是仅退款，不处理';
            return false;
        }

        $refund_only_data = app::get('ome')->model('refund_only')->dump(array('refund_apply_bn' => $refund_apply_bn));
        if ($refund_only_data && $refund_only_data['status'] != 'wait') {
            $msg = '退款单状态不是等待处理，不处理';
            return false;
        }


        $lockKey = 'ome_onlyrefund_process' . $refund_apply_bn;
        base_kvstore::instance('ome_onlyrefund')->fetch($lockKey, $lockValue);
        if ($lockValue) {
            $msg = '退款申请单可能已被其他进程处理';
            return false;
        }

        base_kvstore::instance('ome_onlyrefund')->store($lockKey, time(), 600);

        // 人工确认
        if ($refund_only_data && $refund_only_data['ship_status'] == 'ship_finish_confirm') {
            $refundData['ship_status'] = $refund_only_data['ship_status'];
            $status = $refund_only_data['ship_status'];
        } else {
            // 检查退款申请单的订单是否发货完成
            $status = $this->checkOrderStatus($refundData['order_id']);
            $refundData['ship_status'] = $status;
        }

        $refundData = $this->processlRefundProducts($refundData, $status);

        $refundData = $this->checkRefundItemsPrice($refundData);

        $this->saveOnlyRefundData($refundData);

        if ($refundData['exception_refund']) {
            return $this->addToRefundExceptionList($refundData);
        }

        if ($status == 'ship_not') {
            $this->processUnshippedRefund($refundData, $status);
        } elseif ($status == 'ship_finish' || $status == 'ship_finish_confirm') {
            $this->processShippedRefund($refundData, $status);
        }

        base_kvstore::instance('ome_onlyrefund')->delete($lockKey);

        return true;
    }

    private function checkRefundItemsPrice($refundData) {
        if ($refundData['ship_status'] != 'ship_not_sign') {
            foreach ($refundData['product_data'] as $item) {
                $refund_price = bcdiv($item['refunded'], $item['nums'], 3);
                // 剩余的
                $diffMoney = $refund_price - $item['left_cost_avg'];
                if ( $diffMoney > 0.1 ) {
                    // 异常单据
                    $refundData['exception_refund'] = true;
                    $refundData['exception_refund_reason'] = '退款价格超过订单价格 ' . $item['bn'] . ' 可退单价:' . $item['left_cost_avg'] . ' 退款单价:' . $refund_price;
                    return $refundData;
                }
            }
        }
        return $refundData;
    }

    /**
     * 保存仅退款处理的退款申请单数据
     * @param array $refundData 退款申请单数据
     * @return void
     */
    public function saveOnlyRefundData($refundData)
    {
        kernel::database()->beginTransaction();
        $refund_only_mdl = app::get('ome')->model('refund_only');

        $order = app::get('ome')->model('orders')->dump(array('order_id' => $refundData['order_id']), 'order_bn,shop_id');

        $data = array(
            'refund_apply_bn' => $refundData['refund_apply_bn'],
            'status' => 'wait',
            'ship_status' => $refundData['ship_status'],
            'order_id' => $refundData['order_id'],
            'shop_id' => $order['shop_id'],
            'order_bn' => $order['order_bn'],
            'refunded' => $refundData['refunded'],
            'has_good_refund' => $refundData['has_good_refund'],
            'has_refund_omo_goods' => $refundData['has_refund_omo_goods'] ? '1' : '0',
            'create_time' => $refundData['create_time'],
        );

        if ($data['ship_status'] == 'ship_not') {
            //$data['is_process_invoice'] = '1';
        }

        $info = $refund_only_mdl->dump(array('refund_apply_bn' => $refundData['refund_apply_bn']));
        if ($info) {
            unset($data['create_time']);
            $refund_only_mdl->update($data, array('refund_apply_bn' => $refundData['refund_apply_bn']));
        } else {
            $refund_only_mdl->insert($data);
        }

        // 如果是发货未签收，或者是部分发货，则不需要记录商品，需要重新分摊
        if ($refundData['ship_status'] != 'ship_not_sign') {
            $refund_only_items_mdl = app::get('ome')->model('refund_only_items');
            $is_exist = $refund_only_items_mdl->dump(array('refund_apply_bn' => $refundData['refund_apply_bn']));
            if (!$is_exist && $refundData['product_data']) {
                foreach ($refundData['product_data'] as $val) {
                    $data = array(
                        'refund_apply_bn' => $refundData['refund_apply_bn'],
                        'order_id' => $refundData['order_id'],
                        'bn' => $val['bn'],
                        'nums' => $val['nums'],
                        'obj_id' => $val['obj_id'],
                        'order_item_id' => $val['order_item_id'],
                        'refunded' => $val['refunded'],
                        'refund_price' => bcdiv($val['refunded'], $val['nums'], 3),
                    );
                    $refund_only_items_mdl->save($data);
                }
            }
        }
        kernel::database()->commit();
    }

    public function checkOrderStatus($orderId)
    {
        $orderInfo = app::get('ome')->model('orders')->dump(array('order_id' => $orderId));
        $status = '';
        if ($orderInfo['ship_status'] == '0') {
            $status = 'ship_not'; // 未发货
        } else {
            if ($orderInfo['take_delivery_time']) {
                $status = 'ship_finish'; // 已签收
            } else {
                $status = 'ship_not_sign'; // 未签收
            }
        }
        return $status;
    }

    // 订单签收触发
    public function orderSign($orderId) {
        // 获取所有发货未签收
        $list = app::get('ome')->model('refund_only')->getList('*', array('order_id' => $orderId, 'ship_status' => 'ship_not_sign', 'status' => 'wait'));
        if ($list) {
            foreach ($list as $key => $value) {
                $this->processUnshippedSign($value['refund_apply_bn']);
            }
        }
        return true;
    }

    /**
     * 已发货未签收仅退款
     */
    public function processUnshippedSign($refund_apply_bn) {

        $info = app::get('ome')->model('refund_only')->dump(array('refund_apply_bn' => $refund_apply_bn));
        if (empty($info)) {
            return false;
        }

        if ($info['ship_status'] != 'ship_not_sign') {
            return false;
        }

        // 如果还有商品未发货，则不允许操作
        $itemList = app::get('ome')->model('order_items')->getList('*', array('order_id' => $info['order_id'], 'delete' => 'false'));
        foreach ($itemList as $item) {
            // 有商品未发货，则不处理
            if ($item['nums'] != $item['sendnum']) {
                return false;
            }
        }

        $rs = $this->processByRefund($refund_apply_bn, $msg);
        return $rs;
    }

    /**
     * 已签收仅退款
     */
    public function processShippedRefund($refundData, $status)
    {
        $unshippedTotal = $this->getUnshippedTotal($refundData, $status);
        // 退款金额超过未发货部分，进入异常列表
        $diffMoney = $refundData['refunded'] - $unshippedTotal;
        if ($diffMoney > 0.1) {
            $refundData['exception_refund_reason'] = '退款金额超过大仓商品金额：未退款金额('.$diffMoney.'), 退款金额：'.$refundData['refunded'];
            $this->addToRefundExceptionList($refundData);
            return true;
        }

        // 如果有已签收未发货商品，进入异常列表
        if (!$this->processShippedUnshippedRefund($refundData)) {
            $refundData['exception_refund_reason'] = '已签收未发货商品，不处理';
            $this->addToRefundExceptionList($refundData);
            return true;
        }

        // 处理为成功
        $this->updateRefundSucc($refundData['refund_apply_bn']);

        $this->onlyRefundProcessInvoice($refundData['order_id'], false);

        // 创建3133订单
        $this->saveSap3133($refundData['refund_apply_bn']);
        return true;
    }

    public function processShippedUnshippedRefund($refundData) {

        foreach ($refundData['product_data'] as $item) {
            $order_item_id = $item['order_item_id'];
            $order_item_info = app::get('ome')->model('order_items')->dump(array('item_id' => $order_item_id));
            if ($order_item_info['sendnum'] == 0 || $order_item_info['delete'] == 'true') {
                return false;
            }
        }
        return true;
    }

    /**
     * 修改发票金额
     * @return void
     */
    public function updateInvioceAmount($invoiceId){
        $orderinvoiceMdl = app::get('invoice')->model('orderinvoice');
        $invoice_info = $orderinvoiceMdl->dump(['id' => $invoiceId, 'is_status' => '0', 'sync'=> '0']);
        if ($invoice_info) {
            $sql = "select refunded,id,refund_apply_bn from sdb_ome_refund_only where order_id = {$invoice_info['order_id']} and status='succ' and is_process_invoice='0' and ship_status in ('ship_finish','ship_finish_confirm')";
            $data = kernel::database()->select($sql);
            if (empty($data)) {
                return true;
            }

            $total_refunded = 0;
            $ids = array();
            $refund_apply_bn_array = array();
            foreach ($data as $val) {
                $total_refunded = bcadd($total_refunded, $val['refunded'], 2);
                $ids[] = $val['id'];
                $refund_apply_bn_array[] = $val['refund_apply_bn'];
            }

            $invoiceId = $invoice_info['id'];
            $invoiceNewAmount = $invoice_info['amount'] - $total_refunded;
            // 开始计算新的金额
            $upData['amount'] = bcmul($invoiceNewAmount, 1, 2);
            $upData['without_amount'] = bcmul($upData['amount'] / (1 + $invoice_info['tax_rate'] / 100), 1, 2);      // 不含税金额
            $upData['cost_tax'] = bcmul(($upData['without_amount'] * $invoice_info['tax_rate']) / 100, 1, 2);      // 税金
            $upData['return_amount'] = $upData['amount'];
            // 修改金额
            if ($orderinvoiceMdl->update($upData, ['id' => $invoiceId])) {

                $refund_apply_bn_str = implode(',',$refund_apply_bn_str);

                // 财务记账
                kernel::single('finance_invoice_record')->insertChange($invoiceId, ['before' => $invoice_info['amount'], 'after' => $upData['amount']]);
                $logMsg = "仅退款修改发票金额：原发票含税金额{$invoice_info['amount']}, 仅退款单号:[$refund_apply_bn_str]";
                $opObj = app::get('ome')->model('operation_log');
                $opObj->write_log('invoice_edit@orderinvoice', $invoiceId, $logMsg);

                app::get('ome')->model('refund_only')->update(array('is_process_invoice' => '1'), array('id' => $ids));
            }
        }
        return true;
    }

    // 处理未发货仅退款
    public function processUnshippedRefund($refundData, $status)
    {
        // todo 如果有发货单未成功撤销，直接进入异常列表，因为如果发货成功了，发货单的金额不对

        $unshippedTotal = $this->getUnshippedTotal($refundData, $status);

        $diffMoney = $refundData['refunded'] - $unshippedTotal;
        if ($diffMoney > 0.1) {
            // 退款金额超过未发货部分，进入异常列表
            $refundData['exception_refund_reason'] = '退款金额超过未发货商品金额';
            $this->addToRefundExceptionList($refundData);
            return true;
        }

        // 将订单金额减去退款的商品金额
        $this->processItemLevelRefund($refundData);

        // 处理为成功
        $this->updateRefundSucc($refundData['refund_apply_bn']);

        return true;
    }

    public function onlyRefundProcessInvoice($orderId, $is_process_red_invoice = true)
    {
        // 如果有处理成功，并且为处理开票的单据
        $refund_only_mdl = app::get('ome')->model('refund_only');
        $data = $refund_only_mdl->getList('*', array('order_id' => $orderId, 'is_process_invoice' => '0', 'status' => 'succ', 'ship_status' => array('ship_finish','ship_finish_confirm')));
        $orderinvoiceMdl = app::get('invoice')->model('orderinvoice');
        if ($data) {

            // 中间状态不处理
            $invoice_info = $orderinvoiceMdl->dump(['order_id' => $orderId, 'is_status' => array('3', '4','5')]);
            if ($invoice_info) {
                return true;
            }

            // 中间状态不处理
            $invoice_info = $orderinvoiceMdl->dump(['order_id' => $orderId, 'sync' => array('1', '4')]);
            if ($invoice_info) {
                return true;
            }

            // 未开蓝，修改发票金额
            $invoice_info = $orderinvoiceMdl->dump(['order_id' => $orderId, 'is_status' => '0', 'sync' => '0']);
            if ($invoice_info) {
                $this->updateInvioceAmount($invoice_info['id']);
                return true;
            }

            // 已开蓝，修改发票状态
            $invoice_info = $orderinvoiceMdl->dump(['order_id' => $orderId, 'is_status' => '1', 'sync' => '3']);
            if ($invoice_info) {
                $invoiceId = $invoice_info['id'];
                $invoiceUpdateData['is_status'] = '3';    // 待作废
                $invoiceUpdateData['sync'] = '0';    // 待同步

                $refund_apply_bn = array_column($data, 'refund_apply_bn');
                $logMsg = "仅退款修改发票为待作废：退款单号:{" . implode(',', $refund_apply_bn) . "}";
                $opObj = app::get('ome')->model('operation_log');
                $opObj->write_log('invoice_edit@orderinvoice', $invoiceId, $logMsg);
                $orderinvoiceMdl->update($invoiceUpdateData, array('id' => $invoice_info['id']));
                return true;
            }

            // 红冲成功，重新开蓝
            $invoice_info = $orderinvoiceMdl->dump(['order_id' => $orderId, 'is_status' => '2', 'sync' => '6']);
            if ($invoice_info && $is_process_red_invoice) {
                $err_msg = '';
                kernel::single('invoice_orderprocess')->returnAfterAddNewInvoice($invoice_info['id'], $err_msg);
            }
        }
        return true;
    }

    /**
     * 仅退款,商品级别处理
     */
    private function processItemLevelRefund($refundData)
    {
        $product_data = $refundData['product_data'];
        // 只退一个商品
        foreach ($product_data as $val) {

            $sql = 'select * from sdb_ome_order_items where item_id=' . $val['order_item_id'];
            $row = kernel::database()->selectrow($sql);

            // 如果有实付金额
            if ($row['divide_order_fee'] > 0) {
                $sql = "update sdb_ome_order_items set divide_order_fee=divide_order_fee-" . $val['refunded'] . " where item_id= " . $val['order_item_id'];
                kernel::database()->exec($sql);
            }

            if ($row['cost_avg'] > 0) {
                $price = bcdiv($val['refunded'], $row['nums'], 3);
                $sql = "update sdb_ome_order_items set cost_avg=cost_avg-" . $price . ",real_cost_avg=cost_avg where item_id= " . $val['order_item_id'];
                kernel::database()->exec($sql);
            }
        }
        return true;
    }

    private function getUnshippedTotal($refundData, $shipStatus)
    {
        $orderId = $refundData['order_id'];

        $omeGoods = $this->getRefundOmoGoods($orderId, array());
        $omeGoodsOrderItemIds = $omeGoods['omo_goods_all'];

        // 获取未发货部分总金额
        if ($shipStatus == 'ship_not') {
            $itemList = app::get('ome')->model('order_items')->getList('cost_avg,nums,sendnum', array('order_id' => $orderId, 'sendnum' => 0,'delete' => 'false'));
        } else {
            if ($omeGoodsOrderItemIds) {
                $itemList = app::get('ome')->model('order_items')->getList('cost_avg,nums,sendnum,return_num', array('order_id' => $orderId, 'item_id|notin' => $omeGoodsOrderItemIds, 'delete' => 'false'));
            } else {
                $itemList = app::get('ome')->model('order_items')->getList('cost_avg,nums,sendnum,return_num', array('order_id' => $orderId, 'delete' => 'false'));
            }
        }
        $unshippedTotal = 0;
        foreach ($itemList as $item) {
            if ($shipStatus == 'ship_not') {
                $nums = $item['nums'];
            } else {
                $nums = $item['sendnum'] - $item['return_num'];
            }
            $unshippedTotal += $item['cost_avg'] * $nums;
        }

        $sql = "select sum(refunded) as refunded from sdb_ome_refund_only where order_id = {$orderId} and ship_status in ('ship_finish','ship_finish_confirm','ship_not_sign') and refund_apply_bn != '{$refundData['refund_apply_bn']}'";
        $refundSum = kernel::database()->selectrow($sql);
        return $unshippedTotal - $refundSum['refunded'];
    }

    /**
     * 更新退款状态为成功
     * @param array $refundData 退款单信息
     * @return boolean
     */
    public function updateRefundSucc($refund_apply_bn){
        $refund_only_mdl = app::get('ome')->model('refund_only');
        $rs = $refund_only_mdl->update(array('status' => 'succ'), array('refund_apply_bn' => $refund_apply_bn));
        return $rs;
    }

    private function addToRefundExceptionList($refundData)
    {
        // 将退款添加到异常列表
        $refund_only_mdl = app::get('ome')->model('refund_only');
        $rs = $refund_only_mdl->update(array('status' => 'exception', 'exception_reason' => $refundData['exception_refund_reason']), array('refund_apply_bn' => $refundData['refund_apply_bn']));
        return $rs;
    }

    /**
     * 处理退款商品分摊
     */
    private function processlRefundProducts($refundData, $status)
    {

        $product_data = unserialize($refundData['product_data']);
        $refundData['has_good_refund'] = false;
        if (!empty($product_data)) {
            foreach ($product_data as $key => $val) {
                if (empty($val['bn'])) {
                    unset($product_data[$key]);
                }
            }
        }

        // 如果有退款商品
        if (!empty($product_data)) {
            $refundData['has_good_refund'] = true;
            $refundData['exception_refund'] = false;
            $product_data_tmp = array();
            foreach ($product_data as $val) {
                $items = app::get('ome')->model('order_items')->getList('*', array('bn' => $val['bn'], 'order_id' => $refundData['order_id']));
                if (empty($items)) {
                    $objectInfo = app::get('ome')->model('order_objects')->dump(array('oid' => $val['bn'], 'order_id' => $refundData['order_id']));
                    if ($objectInfo) {
                        $items = app::get('ome')->model('order_items')->getList('*', array('obj_id' => $objectInfo['obj_id'], 'order_id' => $refundData['order_id']));
                    }
                }

                if (empty($items)) {
                    continue;
                }

                // 过滤金额未0单
                $countItemBn = array();
                // 是否有多行
                $isMulti = false;
                foreach ($items as $key => $row) {
                    if ($row['cost_avg'] <= 0 || ($row['nums'] - $row['return_num'] <= 0)) {
                        unset($items[$key]);
                    }

                    $countItemBn[$row['bn']] += 1;
                    if ($countItemBn[$row['bn']] > 1) {
                        $isMulti = true;
                    }
                }

                if (empty($items)) {
                    continue;
                }

                if ($isMulti) {
                    // 异常单据
                    $refundData['exception_refund'] = true;
                    $refundData['exception_refund_reason'] = '退款货号存在多行';
                }

                foreach ($items as $row) {

                    if ($status == 'ship_not') {
                        $nums = $row['nums'];
                    } else {
                        $nums = $row['nums'] - $row['return_num'];
                    }

                    if ($nums <= 0) {
                        continue;
                    }

                    // 用于分摊优惠金额的计算
                    $sql = "select sum(refund_price) as refunded from sdb_ome_refund_only_items i left join sdb_ome_refund_only o on i.refund_apply_bn=o.refund_apply_bn where i.order_item_id={$row['item_id']} and o.status='succ' and o.ship_status in ('ship_finish','ship_finish_confirm')";
                    $itemRefundSum = kernel::database()->selectrow($sql);
                    $total_cost_avg = 0;
                    if ($itemRefundSum) {
                        // 超过则不分摊
                        if ($row['cost_avg'] - $itemRefundSum['refunded'] <= 0) {
                            continue;
                        }
                        $total_cost_avg = bcmul(($row['cost_avg']-$itemRefundSum['refunded']), $nums, 3);
                        // 剩余可退的金额
                        $row['left_cost_avg'] = $row['cost_avg'] - $itemRefundSum['refunded'];
                    } else {
                        $total_cost_avg = bcmul($row['cost_avg'], $nums, 3);
                        $row['left_cost_avg'] = $row['cost_avg'];
                    }

                    $product_data_tmp[] = array(
                        'order_item_id' => $row['item_id'],
                        'product_id'    => $row['product_id'],
                        'bn'            => $row['bn'],
                        'name'          => $val['name'],
                        'nums'          => $nums,
                        'cost_avg'      => $row['cost_avg'],
                        'left_cost_avg' => $row['left_cost_avg'],
                        'total_cost_avg' => $total_cost_avg,
                        'obj_id'        => $row['obj_id'],
                    );
                }
            }

            if ($product_data_tmp) {
                $options = array(
                    'part_total'  => $refundData['refunded'],
                    'part_field'  => 'refunded', // 商品明细退款金额，总价
                    'porth_field' => 'total_cost_avg',
                );
                $product_data = kernel::single('ome_order')->calculate_part_porth($product_data_tmp, $options, 3);
                $refundData['product_data'] = $product_data;
            } else {
                // 异常单据
                $refundData['exception_refund'] = true;
                $refundData['exception_refund_reason'] = '没有可退的商品';
            }

            if ($status == 'ship_not') {
                return $refundData;
            }
        }

        if ($status != 'ship_not') {
            $refundOmoGoods = $this->getRefundOmoGoods($refundData['order_id'], $product_data);
            $refundData['has_refund_omo_goods'] = $refundOmoGoods['is_omo_refund'];
            $refundData['omo_goods'] = $refundOmoGoods['omo_goods'];
            $refundData['omo_goods_all'] = $refundOmoGoods['omo_goods_all'];
            if ($refundData['has_refund_omo_goods']) {
                $product_data = array();
            }
        }

        if (empty($product_data)) {
            // 设置退款类型：商品仅退款，订单级别仅退款
            $orderId = $refundData['order_id'];
            // 退款金额按照未发货的商品金额进行分摊
            if ($status == 'ship_not') {
                $itemList = app::get('ome')->model('order_items')->getList('*', array('order_id' => $orderId, 'sendnum' => 0,'delete'=>'false'));
            } else {
                // 如果是已发货，则把金额分摊到大仓商品
                if ($refundData['omo_goods_all']) {
                    $itemList = app::get('ome')->model('order_items')->getList('*', array('order_id' => $orderId, 'item_id|notin' => $refundData['omo_goods_all'], 'delete' => 'false'));
                } else {
                    $itemList = app::get('ome')->model('order_items')->getList('*', array('order_id' => $orderId, 'delete' => 'false'));
                }
            }

            if (empty($itemList)) {
                // 异常单据
                $refundData['exception_refund'] = true;
                $refundData['exception_refund_reason'] = '没有可分摊的商品1';
                return $refundData;
            }

            foreach ($itemList as $key => $row) {
                if ($status == 'ship_not') {
                    $nums = $row['nums'];
                } else {
                    $nums = $row['nums'] - $row['return_num'];
                }

                if ($nums <= 0) {
                    unset($itemList[$key]);
                    continue;
                }

                // 用于分摊优惠金额的计算
                $sql = "select sum(refund_price) as refunded from sdb_ome_refund_only_items i left join sdb_ome_refund_only o on i.refund_apply_bn=o.refund_apply_bn where i.order_item_id={$row['item_id']} and o.status='succ' and o.ship_status in ('ship_finish','ship_finish_confirm')";
                $itemRefundSum = kernel::database()->selectrow($sql);
                $total_cost_avg = 0;
                if ($itemRefundSum) {
                    // 超过则不分摊
                    if ($row['cost_avg'] - $itemRefundSum['refunded'] <= 0) {
                        unset($itemList[$key]);
                        continue;
                    }
                    $total_cost_avg = bcmul(($row['cost_avg'] - $itemRefundSum['refunded']), $nums, 3);
                    $row['left_cost_avg'] = $row['cost_avg'] - $itemRefundSum['refunded'];
                } else {
                    $total_cost_avg = bcmul($row['cost_avg'], $nums, 3);
                    $row['left_cost_avg'] = $row['cost_avg'];
                }

                $itemList[$key]['left_cost_avg'] = $row['left_cost_avg'];
                $itemList[$key]['new_num'] = $nums;
                $itemList[$key]['total_cost_avg'] = $total_cost_avg;
            }

            if (empty($itemList)) {
                // 异常单据
                $refundData['exception_refund'] = true;
                $refundData['exception_refund_reason'] = '没有可分摊的商品';
                return $refundData;
            }

            $options = array(
                'part_total'  => $refundData['refunded'],
                'part_field'  => 'refunded',
                'porth_field' => 'total_cost_avg',
            );

            $refundData['product_data'] = array();
            $items = kernel::single('ome_order')->calculate_part_porth($itemList, $options, 3);
            $product_data = array();
            foreach ($items as $item) {
                $product_data[] = array(
                    'order_item_id' => $item['item_id'],
                    'product_id'    => $item['product_id'],
                    'bn'            => $item['bn'],
                    'name'          => $item['name'],
                    'nums'          => $item['new_num'],
                    'refunded'      => $item['refunded'],
                    'left_cost_avg' => $item['left_cost_avg'],
                    'obj_id'        => $item['obj_id'],
                    'modified'      => time(),
                );
            }
            $refundData['product_data'] = $product_data;
        }
        return $refundData;
    }

    public function getRefundOmoGoods($orderId, $product_data) {
        $autoDlyJXSList = kernel::database()->select("
            select d.delivery_id from sdb_ome_delivery_order do
            left join sdb_ome_delivery d on do.delivery_id=d.delivery_id
            where d.branch_type = '2' and do.order_id = {$orderId} and d.status='succ'");

        $omoGoods = array();
        $orderItemIds = array();
        // 经销商发货单
        if(!empty($autoDlyJXSList)) {
            $deliveryIds = array_column($autoDlyJXSList, 'delivery_id');
            $deliveryItems = app::get('ome')->model('delivery_items')->getList('order_item_id', array('delivery_id' => $deliveryIds));
            $orderItemIds = array_column($deliveryItems, 'order_item_id');
            foreach ($product_data as $key => $val) {
                if (in_array($val['order_item_id'], $orderItemIds)) {
                    $omoGoods[] = $val['order_item_id'];
                }
            }
        }

        $is_omo_refund = false;
        if (!empty($omoGoods)) {
            $is_omo_refund = true;
        }

        $omoGoods = array_unique($omoGoods);
        return array('is_omo_refund' => $is_omo_refund, 'omo_goods' => $omoGoods, 'omo_goods_all'=>$orderItemIds);
    }

    public function resetReshipItemMoney($reship_id) {

        $reshipItems = app::get('ome')->model('reship_items')->getList('order_item_id', array('reship_id' => $reship_id));
        if (empty($reshipItems)) {
            return true;
        }

        $order_item_id = array_column($reshipItems, 'order_item_id');
        $sql = "select order_item_id,refund_price from sdb_ome_refund_only_items i left join sdb_ome_refund_only r on i.refund_apply_bn=r.refund_apply_bn where r.ship_status in ('ship_finish','ship_finish_confirm') and r.status='succ' and order_item_id in (" . implode(",", $order_item_id) . ")";
        $list = kernel::database()->select($sql);
        if (empty($list)) {
            return true;
        }

        $refund_price_arr = array();
        foreach ($list as $key => $item) {
            if (!isset($refund_price_arr[$item['order_item_id']])) {
                $refund_price_arr[$item['order_item_id']] = 0;
            }
            $refund_price_arr[$item['order_item_id']] = bcadd($refund_price_arr[$item['order_item_id']], $item['refund_price'], 3);
        }

        if ($refund_price_arr) {
            foreach ($refund_price_arr as $order_item_id => $refund_price) {
                $reshipItem = app::get('ome')->model('reship_items')->dump(array('order_item_id' => $order_item_id, 'reship_id' => $reship_id), '*');
                $orderItem = app::get('ome')->model('order_items')->dump(array('item_id' => $order_item_id), '*');
                if ($reshipItem['price'] == $orderItem['cost_avg']) {
                    $sql = "update sdb_ome_reship_items set price=price-" . $refund_price . ",amount=price*num where order_item_id=" . $order_item_id . " and reship_id=" . $reship_id;
                    kernel::database()->exec($sql);
                }
            }
        }

        return true;
    }

    /**
     * Resets the invoice items based on the provided delivery item list.
     *
     * @param array $wmsDeliveryItemList List of delivery items from WMS.
     * @return void
     */
    public function resetInvoiceItems($wmsDeliveryItemList) {
        $newWmsDeliveryItemList = array();
        foreach ($wmsDeliveryItemList as $key => $item) {
            $item['cost_avg_key'] = $item['cost_avg'];
            if (isset($newWmsDeliveryItemList[$item['order_item_id']])) {
                $newWmsDeliveryItemList[$item['order_item_id']]['number'] += $item['number'];
            } else {
                $newWmsDeliveryItemList[$item['order_item_id']] = $item;
            }
        }

        $order_item_id = array_keys($newWmsDeliveryItemList);
        $sql = "select order_item_id,i.refund_price,i.refunded,i.refund_price from sdb_ome_refund_only_items i left join sdb_ome_refund_only r on i.refund_apply_bn=r.refund_apply_bn where r.status='succ' and r.is_process_invoice='1' and r.ship_status in ('ship_finish','ship_finish_confirm') and r.status='succ' and order_item_id in (" . implode(",", $order_item_id) . ")";
        $list = kernel::database()->select($sql);
        if ($list) {
            $refundList = array();
            foreach ($list as $item) {
                if (isset($refundList[$item['order_item_id']])) {
                    $refundList[$item['order_item_id']] = bcadd($refundList[$item['order_item_id']], $item['refund_price'], 3);
                } else {
                    $refundList[$item['order_item_id']] = $item['refund_price'];
                }
            }

            foreach ($newWmsDeliveryItemList as $key => $item) {
                if (isset($refundList[$item['order_item_id']])) {
                    $newWmsDeliveryItemList[$key]['cost_avg'] -= $refundList[$item['order_item_id']];
                }
            }

            return $newWmsDeliveryItemList;
        } else {
            return $wmsDeliveryItemList;
        }
    }

    public function saveSap3133($refund_apply_bn) {

        $refund_only_mdl = app::get('ome')->model('refund_only');
        $data = $refund_only_mdl->dump(array('refund_apply_bn' => $refund_apply_bn), '*');

        if ($data['status'] != 'succ') {
            return true;
        }

        if ( !in_array($data['ship_status'], ['ship_finish', 'ship_finish_confirm']) ) {
            return true;
        }

        $shop = app::get('ome')->model('shop')->dump(array('shop_id' => $data['shop_id']));
        $orderinfo = app::get('ome')->model('orders')->dump(array('order_id' => $data['order_id']), 'werks,order_bn');

        $refundItemList = app::get('ome')->model('refund_only_items')->getList('*', array('refund_apply_bn' => $refund_apply_bn));
        $order_item_id = array_column($refundItemList, 'order_item_id');

        $sql = "select i.* from sdb_ome_delivery_items i left join sdb_ome_delivery d on i.delivery_id=d.delivery_id where d.status='succ' and i.order_item_id in (". implode(",",$order_item_id) .")";
        $deliveryItemList = kernel::database()->select($sql);

        $delivery_item_id = array_column($deliveryItemList, 'item_id');
        $sql = "select bn_91,delivery_outer_item_id from sdb_finance_sum_record_items where type='delivery' and delivery_outer_item_id in (". implode(",",$delivery_item_id) .") and order_bn='" . $orderinfo['order_bn'] . "'";
        $financeItemList = kernel::database()->select($sql);
        $bn91Arr = array();
        if ($financeItemList) {
            foreach ($financeItemList as $row) {
                if ($row['bn_91']) {
                    $bn91Arr[$row['delivery_outer_item_id']] = $row['bn_91'];
                }
            }
        }

        $bn91ArrByOrderItemId = array();
        foreach ($deliveryItemList as $row) {
            if (isset($bn91Arr[$row['item_id']])) {
                $bn91ArrByOrderItemId[$row['order_item_id']] = $bn91Arr[$row['item_id']];
            }
        }

        $deliveryItemList = array_column($deliveryItemList, null, 'order_item_id');
        $i = 1;
        foreach ($refundItemList as $row) {

            $materialInfo = app::get('material')->model('basic_material')->dump([
                'material_bn' => $row['bn'],
            ], 'bm_id');

            $materialExtInfo = array();
            if ($materialInfo) {
                $materialExtInfo = app::get('material')->model('basic_material_ext')->dump([
                    'bm_id' => $materialInfo['bm_id'],
                ], 'unit');
            }

            $bacodeInfo = app::get('ome')->model('reship_batchcode')->dump(array('bn'=>trim($row['bn'])), 'batch_code');
            $itemData = array(
                'line' => $i,
                'shop_id' => $data['shop_id'],
                'order_id' => $data['order_id'],
                'order_origin' => $shop['order_origin'],
                'werks' => $orderinfo['werks'],
                'refund_apply_bn' => $data['refund_apply_bn'],
                'bn' => $row['bn'],
                'number' => $row['nums'],
                'refund_price' => $row['refund_price'],
                'refunded' => $row['refunded'],
                'bn_91' => isset($bn91ArrByOrderItemId[$row['order_item_id']]) ? $bn91ArrByOrderItemId[$row['order_item_id']] : '',
                'tosap_status' => '1',
                'sap_sku' => $deliveryItemList[$row['order_item_id']]['sap_sku'],
                'unit' => $materialExtInfo ? $materialExtInfo['unit'] : '',
                'batch_code' => $bacodeInfo ? $bacodeInfo['batch_code'] : '',
                'order_item_id' => $row['order_item_id'],
                'create_time' => time()
            );

            $finance_refund_only_items_mdl = app::get('finance')->model('refundonly');
            $finance_refund_only_items_mdl->insert($itemData);
            $i++;
        }
        return true;
    }
}
