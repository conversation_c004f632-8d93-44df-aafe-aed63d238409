<?php
/**
 * 系统自动审单
 *
 * @access public
 * <AUTHOR>
 * @version $Id: consign.php 2016-10-20
 */
class erpapi_shop_response_plugins_order_combine extends erpapi_shop_response_plugins_order_abstract
{
    public function convert(erpapi_shop_response_abstract $platform)
    {
        $combine    = array();


        #开启系统自动审单(默认:忽略可合并的订单)
        $cfg_combine    = app::get('ome')->getConf('ome.order.is_auto_combine');
        //$cfg_merge      = app::get('ome')->getConf('ome.order.is_merge_order');//忽略可合并的订单
        
        if($cfg_combine == 'true')
        {
            //过滤手动拉下来的订单
            // if($platform->_ordersdf['auto_combine'] !== false)
            // {
            //     $combine['order_id']    = null;
            // }
            //@Author: <PERSON><PERSON>  @Description: 自动审单  @DateTime: 2018-09-05 19:05:35
            if ($platform->_ordersdf['order_bn']) {
                $combine['order_id'] = $platform->_ordersdf['order_bn'];
            }
        }

        // $dir = DATA_DIR.'/combine_0.log';
        // file_put_contents($dir,"\n\n\n".json_encode($combine),FILE_APPEND);

        if($platform->_ordersdf['cnAuto'] == 'true'){
            $combine['cnAuto'] = 'true';
        }
        return $combine;
    }
    
    /**
     * 订单完成后处理
     *
     * @return void
     * <AUTHOR>
    public function postCreate($order_id, $combine)
    {
        $pay_status    = false;

        //支付状态读取订单表(预售功能定制)
        $order_info    = app::get('ome')->model('orders')->dump(array('order_id'=>$order_id), 'shop_id,pay_status,status,order_bool_type,shop_type,paytime');
        
        //订单必须已支付OR货到付款,并且过滤单拉的订单
        if(($order_info['pay_status'] == '1' || $order_info['shipping']['is_cod'] == 'true') && $order_info['status'] == 'active')
        {
            $delayTimeList = (array)app::get('ome')->getConf('ome.order.delay_exec_timer');
            $delayTimeList = array_column($delayTimeList, null, 'shop_id');
            if (isset($delayTimeList['all']) || isset($delayTimeList[$order_info['shop_id']])) {
                $hour = $delayTimeList['all']['timer'] ?: $delayTimeList[$order_info['shop_id']]['timer'];
                $insertData = array('order_id'=>$order_id, 'delay_time'=>$order_info['paytime'] + $hour*3600,'createtime'=>time());
                app::get('ome')->model('order_delay')->insert($insertData);
                return;
            }
            // 如果是拼多多风控订单 由于没有收货人信息不审单
            if ($order_info['shop_type'] == 'pinduoduo' && ($order_info['order_bool_type'] & ome_order_bool_type::__RISK_CODE) == ome_order_bool_type::__RISK_CODE) {
                return;
            }
            // $dir = DATA_DIR.'/combine_1.log';
            // file_put_contents($dir,"\n\n\n".json_encode($combine),FILE_APPEND);
            //执行自动审单
            kernel::single('ome_order')->auto_order_combine($order_id,$combine);
        }
    }

    public function postUpdate($order_id, $params)
    {
        $order_info    = app::get('ome')->model('orders')->dump(array('order_id'=>$order_id), 'pay_status,status,ship_area,order_bool_type,shop_type,process_status');
        //订单必须已支付OR货到付款,并且过滤单拉的订单
        if(($order_info['pay_status'] == '1' || $order_info['shipping']['is_cod'] == 'true') && $order_info['status'] == 'active')
        {
            // 如果是拼多多风控订单 有收货人信息解除风控审单 未确认
            if ($order_info['shop_type'] == 'pinduoduo' && $order_info['process_status'] == 'unconfirmed' && $order_info['consignee']['area'] && ($order_info['order_bool_type'] & ome_order_bool_type::__RISK_CODE) == ome_order_bool_type::__RISK_CODE) {
                kernel::single('ome_order')->auto_order_combine($order_id,$params);
            }
        }
    }
}