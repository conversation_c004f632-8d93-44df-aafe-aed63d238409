<?php
/**
 * @desc
 * @author: jintao
 * @since: 2016/7/21
 */
class erpapi_shop_response_process_aftersalev2 {

    private $editorderDeletedBns = [];

    public function add($sdf) {
        switch($sdf['response_bill_type']) {
            case 'refund_apply' :
                $rs = $this->_dealRefundApply($sdf);
                break;
            case 'refund' :
                $rs = $this->_dealRefund($sdf);
                break;
            case 'return_product' :
                $rs = $this->_dealReturnProduct($sdf);
                break;
            case 'reship' :
                $rs = $this->_dealReship($sdf);
                break;
            case 'miaozu_only_refund':
                $rs = $this->_dealMiaozuRefund($sdf);
                break;
            default :
                $rs = array('rsp'=>'fail', 'msg'=>'没有单据类型');
        }
        return $rs;
    }

    private function _dealRefundApply($sdf) {
        if ($sdf['refund']) {
            return $this->_hadRefund($sdf);
        }
        $modelRefundApply = app::get('ome')->model('refund_apply');
        $oOperation_log = app::get('ome')->model('operation_log');//写日志
        if($sdf['refund_apply']) {
            $refundApply = $sdf['refund_apply'];
            $filter = array('apply_id' => $refundApply['apply_id']);
            switch($sdf['status']) {
                case '0':
                    $upData = $this->_refundApplySdfToData($sdf);
                    $memo = '(退款金额、原因或版本变化)退款申请单更新为未审核';
                    break;
                default :
                    $upData = array(
                        'status' => $sdf['status'],
                        'outer_lastmodify'=> $sdf['modified'],
                    );
                    if($sdf['refund_version_change']) {
                        $upData['memo']  = $sdf['reason'];
                        $upData['money'] = $sdf['refund_fee'];
                    } else {
                        $filter['memo'] = $sdf['reason'];
                        $filter['money'] = $sdf['refund_fee'];
                    }
                    $memo = '更新成功,状态：' . $sdf['status'];
                    break;
            }

            $rs = $modelRefundApply->update($upData, $filter);
            $idBn = array(
                'apply_id' => $refundApply['apply_id'],
                'refund_apply_bn' => $upData['refund_apply_bn']
            );
            $this->_dealTableAdditional($sdf['table_additional'], $idBn);
            if(!is_bool($rs)) {
                $oOperation_log->write_log('refund_apply@ome', $refundApply['apply_id'], $memo);
            }

            //更新订单支付状态、叫回发货单等
            kernel::single('ome_order_func')->update_order_pay_status($sdf['order']['order_id']);

        } else {
            $insertData = $this->_refundApplySdfToData($sdf);

            //创建退款单
            $is_update_order    = true;//是否更新订单付款状态
            $rs = kernel::single('ome_refund_apply')->createRefundApply($insertData, $is_update_order, $error_msg);

            if(!$rs) {
                return array('rsp'=>'fail', 'msg'=>'退款申请单生成失败');
            }

            $idBn = array(
                'apply_id' => $insertData['apply_id'],
                'refund_apply_bn' => $insertData['refund_apply_bn']
            );
            $this->_dealTableAdditional($sdf['table_additional'], $idBn);
            $memo = '创建退款申请单,状态：' . $sdf['status'].$sdf['memo'];
            $oOperation_log->write_log('refund_apply@ome',$insertData['apply_id'],$memo);
        }

        //识别是否天猫退款触发AG接口
        $noticeParams = array_merge($sdf, $idBn);
        $this->_noticeAg($noticeParams);

        return array('rsp'=>'succ', 'msg'=>$memo);
    }

    private function _refundApplySdfToData($sdf) {
        $data = array(
            'order_id'        => $sdf['order']['order_id'],
            'refund_apply_bn' => $sdf['refund_bn'],
            'pay_type'        => $sdf['pay_type'],
            'account'         => $sdf['account'],
            'bank'            => $sdf['bank'],
            'pay_account'     => $sdf['pay_account'],
            'money'           => $sdf['refund_fee'],
            'refunded'        => '0',
            'memo'            => $sdf['reason'],
            'create_time'     => $sdf['created'],
            'status'          => $sdf['status'],
            'shop_id'         => $sdf['shop_id'],
            'addon'           => serialize(array('refund_bn'=>$sdf['refund_bn'])),
            'source'          => 'matrix',
            'shop_type'       => $sdf['shop_type'],
            'outer_lastmodify'=> $sdf['modified'],
            'refund_refer'    => in_array($sdf['order']['ship_status'],array('1','3')) ? '1' : '0',
            'refund_oid'      => isset($sdf['refund_item_list'][0]['oid'])?$sdf['refund_item_list'][0]['oid']:'',
            'refund_bn'       => isset($sdf['refund_item_list'][0]['bn'])?$sdf['refund_item_list'][0]['bn']:'',
            'refund_num'      => isset($sdf['refund_item_list'][0]['num'])?$sdf['refund_item_list'][0]['num']:0,
        );
        if($sdf['refund_item_list']) {
            $arrProduct = array();
            foreach($sdf['refund_item_list'] as $val) {
                $arrProduct[] = array(
                    'product_id'    => $val['product_id'],
                    'bn'            => $val['bn'],
                    'name'          => $val['title'] ? $val['title'] : $val['name'],
                    'num'           => $val['num'],
                    'price'         => $val['price'],
                    'oid'           => $val['oid'],
                    'refund_phase'  => $val['refund_phase'],
                    'refund_memo'   => $val['refund_memo'],
                    'modified'      => kernel::single('ome_func')->date2time($val['modified']),
                );
            }
            $data['product_data'] = serialize($arrProduct);
        }
        return $data;
    }

    private function _dealTableAdditional($tableAdditional, $idBn) {
        if(empty($tableAdditional) || empty($idBn)) {
            return false;
        }
        $model = app::get('ome')->model($tableAdditional['model']);
        if($tableAdditional['model'] == 'return_apply_special') {
            $old = $model->db_dump($idBn, 'id');
            if($old) {
                $model->update($tableAdditional['data'], array('id'=>$old['id']));
                return;
            }
        }
        $data = array_merge($tableAdditional['data'], $idBn);
        $model->db_save($data);
    }

    #退款单已经存在处理
    private function _hadRefund($sdf) {
        $modelRefundApply = app::get('ome')->model('refund_apply');
        $oOperation_log = app::get('ome')->model('operation_log');//写日志
        $msg = '退款单' . $sdf['refund']['refund_bn'] . '已经存在';
        if($sdf['refund_apply'] && $sdf['refund_apply']['status'] != '4') {
            $modelRefundApply->update(array('status' => '4'), array('apply_id' => $sdf['refund_apply']['apply_id']));
            $msg .= '  更新退款申请单为已退款';
            $oOperation_log->write_log('refund_apply@ome', $sdf['refund_apply']['apply_id'], '退款单已存在，自动更新为已退款');
        }
        return array('rsp'=>'succ', 'msg'=>$msg);
    }

    private function _dealRefund($sdf) {
        if ($sdf['refund']) {
            return $this->_hadRefund($sdf);
        }
        $data = $this->_refundsdfToData($sdf);
        $rs = app::get('ome')->model('refunds')->insert($data);
        if(!$rs) {
            return array('rsp'=>'fail', 'msg'=>'退款单创建失败');
        }
        $msg = '创建退款单';
        $refundApply = $sdf['refund_apply'];
        if(!$refundApply) {
            $applyData = $this->_refundApplySdfToData($sdf);
            $modelRefundApply = app::get('ome')->model('refund_apply');
            $insertRes = $modelRefundApply->insert($applyData);
            if (!$insertRes) {
                $applyData = $modelRefundApply->db_dump(array('refund_apply_bn' => $sdf['refund_bn']));
            }
            $refundApply = $sdf['refund_apply'] = $applyData;
            kernel::single('base_customlog')->log(['$refundApply'=>$refundApply, '$insertRes'=>$insertRes, '$sdf'=>$sdf], "aftersalev2/_dealRefund");
        }
        if(!$refundApply) {
            $applyData = $this->_refundApplySdfToData($sdf);
            $modelRefundApply = app::get('ome')->model('refund_apply');
            $insertRes = $modelRefundApply->insert($applyData);
            if (!$insertRes) {
                $applyData = $modelRefundApply->db_dump(array('refund_apply_bn' => $sdf['refund_bn']));
            }
            $refundApply = $sdf['refund_apply'] = $applyData;
            kernel::single('base_customlog')->log(['$refundApply'=>$refundApply, '$insertRes'=>$insertRes, '$sdf'=>$sdf], "aftersalev2/_dealRefund");
        }
        if ($refundApply) {
            $filter = array(
                'apply_id' => $refundApply['apply_id'],
            );
            $updateData = array('status' => '4','refunded' => $sdf['refund_fee']);
            app::get('ome')->model('refund_apply')->update($updateData,$filter);
            app::get('ome')->model('operation_log')->write_log('refund_apply@ome', $refundApply['apply_id'], '退款成功');
            $msg .= "&nbsp;&nbsp;更新退款申请单[{$refundApply['refund_apply_bn']}]为已退款"; 

            if ($refundApply['addon']) {
                $addon = unserialize($refundApply['addon']);
                $return_id = $addon['return_id'];
                if ($return_id) {
                    $pReturnModel = app::get('ome')->model('return_product');
                    $pReturnData = $pReturnModel->getList('refundmoney,return_bn', array('return_id' => $return_id), 0, 1);
                    $pReturn = $pReturnData[0];
                    $refundMoney = bcadd((float)$sdf['refund_fee'], (float)$pReturn['refundmoney'],3);
                    $pReturnModel->update(array('refundmoney'=>$refundMoney),array('return_id'=>$return_id));
                    $return_bn = $pReturn['return_bn'];
                    $msg .= "&nbsp;&nbsp;更新售后申请单[{$return_bn}]金额：".$refundMoney;

                    //如果是售后退货完成产生的退款完成更新，生成售后单
                    if($sdf['tmall_has_finished_return_product']){
                        kernel::single('sales_aftersale')->generate_aftersale($refundApply['apply_id'],'refund');
                    }
                }
            }

            // ----- start <AUTHOR> @Description 退款申请单状态为退款成功的时候，将订单里相关的退款物料的字段delete标记为true
            if($sdf['status'] == '4' || $sdf['status'] == 4){
                kernel::single('ome_refund_apply')->deleteOrderGoods($refundApply['apply_id'],$sdf['order']['order_id'],$sdf['refund_item_list']);
            }
            // ----- end

        }
        if($this->_updateOrderPayed($sdf['order']['order_id'],$sdf['refund_fee'])) {
            $msg .= '&nbsp;&nbsp;更新订单支付状态';
        }
        // 全额退款 拦截发货单
        $this->_checkIsFullRefund($data['order_id'], $refundApply);

        # 如果 (是部分退款订单，并且退款完成，并且发货状态是未发货或部分发货)，就 {自动对订单做相关处理} ------
        $order_detail = app::get('ome')->model('orders')->dump(array('order_id'=>$sdf['order']['order_id']), '*');
        kernel::single('base_customlog')->log('开始自动编辑订单外部'.json_encode($sdf).json_encode($order_detail), "aftersalev2/out");
        if($order_detail['pay_status'] == '4' && $sdf['status'] == '4' && in_array($order_detail['ship_status'], array('0','2'))) { // 部分退款 && 部分退款状态为完成 && 未发货或部分发货
            $error_msg = '';
            $isResultEdit = $this->_autoEditorder($sdf, $error_msg);
            if(!$isResultEdit){
                $msg .= '&nbsp;&nbsp;'. $error_msg;
            }
        }

        $errmsgsByRefundMsg = '';
        kernel::single('ome_onlyrefund')->processByRefund($sdf['refund_bn'], $errmsgsByRefundMsg);

        return array('rsp'=>'succ', 'msg'=>$msg.'ccc');
    }

    private function checkEditOrder($sdf, &$error_msg=null){
        $order_id = $sdf['order']['order_id'];

        //check支持的平台
        $order_detail = app::get('ome')->model('orders')->dump(array('order_id'=>$order_id), '*');
        if(!in_array($order_detail['shop_type'], array('taobao','tmall','luban','website_d1m','website'))){
            $error_msg =  '[退款编辑订单]：'.$order_detail['shop_type'].'平台订单不支持编辑';
            $this->log('order_edit@ome', $order_id, $error_msg);
            return false;
        }

        //check订单来源
        if($order_detail['source'] != 'matrix'){
            $error_msg = '[退款编辑订单]：订单来源不是matrix类型';
            $this->log('order_edit@ome', $order_id, $error_msg);
            return false;
        }

        //天猫只退款价保金额时,不编辑订单明细
        if($sdf['isPriceProtect']){
            $error_msg = '[退款编辑订单]：只退款价保金额,不允许编辑订单';
            $this->log('order_edit@ome', $order_id, $error_msg);
            return false;
        }

        //check过滤不需要删除订单明细的退款申请备注
        $reason = $sdf['reason'];
        if($reason){
            $reasonList = array('退运费', '申请价保退款', '价保退款');
            if(in_array($reason, $reasonList)){
                $error_msg = '[退款编辑订单]：退款原因：'. $reason .',不允许编辑订单';
                $this->log('order_edit@ome', $order_id, $error_msg);
                return false;
            }
        }

        return true;
    }

    /**
     * 平台推送的已退款单,需要编辑订单删除退款的商品
     *
     * @param $sdf 平台推送的退款数据
     * @param $error_msg 错误信息
     * @param $is_abnormal 是否为异常(订单已生成发货单,平台已退款但撤消发货单失败,导致删除订单退款商品失败)
     * @return bool
     */
    private function _autoEditorder($sdf, &$error_msg=null){
        $GLOBALS['_LOG'] = 'aftersalev2';
        kernel::single('base_customlog')->log('开始自动编辑订单'.json_encode($sdf), "aftersalev2/in");
        kernel::single('base_customlog')->log(PHP_EOL.PHP_EOL.'开始处理订单'.json_encode($sdf['order_bn']."订单内容为".json_encode($sdf)), "aftersalev2/order_edit@ome");

        # 验证 ------
        if(!$this->checkEditOrder($sdf, $error_msg)) return false;
        $order_id = $sdf['order']['order_id'];
        $order_detail = app::get('ome')->model('orders')->dump(array('order_id'=>$order_id), '*');

        # 如果退款商品未发货，就订单删除已退款商品 ------
        if(!$this->deleteOrderItems($order_id, $sdf, $error_msg)) return false;
        $unShipNum = app::get('ome')->model('delivery')->countOrderSendNumber($order_id); // 查询未退款商品是否已经发货完毕

        # 开启事务 ------
        $db = kernel::database();
        $trans = $db->beginTransaction();//事务开始

        # 如果已经有发货单的撤销相关发货单 ------
        $rs = app::get('ome')->model('orders')->cancel_delivery($order_id);
        $error_msg = "[退款编辑订单]撤销发货单".(($rs['rsp'] != 'succ') ? "失败" : "成功");
        $this->log('order_edit@ome', $order_id, $error_msg);
        if($rs['rsp'] != 'succ'){
            return false;
        }

        # （情况一）部分退货情况下，如果未退款商品都发货完毕了，就走：[余单撤销逻辑] ------
        if($order_detail['ship_status'] == '2' && $unShipNum == 0){
            $this->log('order_edit@ome', $order_id, '[退款编辑订单]未退款商品已发完,进入余单撤销');

            # 余单撤销 ------
            $diff_price = kernel::single('ome_order_func')->order_items_diff_money($order_id);
            $order['diff_price'] = $diff_price;
            if ($order['payed'] > $diff_price) {
                $refund_money = $diff_price;
            } else {
                $refund_money = $order['payed'];
            }
            $rs = kernel::single('ome_order_order')->order_revoke($order_id, $refund_money, $diff_price);
            $error_msg = "[退款编辑订单]余单撤销".(!$rs ? "失败" : "成功");
            $this->log('order_edit@ome', $order_id, $error_msg);
            if(!$rs){
                return false;
            }

            # 改变订单状态为整单发货 ------
            $update_sql = "UPDATE sdb_ome_orders SET `ship_status`='1' WHERE order_id=". $order_id;
            $affect_row = $db->exec($update_sql);
            $error_msg = "[退款编辑订单]编辑订单发货状态".(!$affect_row ? "失败" : "成功");
            $this->log('order_edit@ome', $order_id, $error_msg);
            if(!$affect_row){
                return false;
            }

            # 开发票 ------
            $rs = kernel::single('console_repair_createOrderinvoice')->exec($order_detail['order_bn']);
            $error_msg = "[退款编辑订单]生成发票数据".(!$rs ? "失败" : "成功");
            $this->log('order_edit@ome', $order_id, '[退款编辑订单]生成发票数据失败');
            if(!$rs){
                return false;
            }

        }else{
            $orderObj = app::get('ome')->model('orders');
            $orderInfo = $orderObj->dump(array('order_id'=>$order_id), 'order_bool_type, mark_text, auto_status');
            if ($orderInfo['order_bool_type'] & ome_order_bool_type::__DELETE_ITEM_FAIL_CODE) {
                $this->log('order_edit@ome', $order_id, '[退款编辑订单]删除商品失败，不自动审核');
            } else {
                //更改订单状态
                app::get('ome')->model('orders')->update(['op_id'=>null,'group_id'=>null],['order_id'=>$order_id]);
                # （情况二）其余情况就走: [自动审单逻辑] ------
                $is_one_bn_match_many_color = $this->is_one_bn_match_many_color($order_id);
                if(!$is_one_bn_match_many_color) {
                    kernel::single('ome_order')->auto_order_combine($order_id);
                    $this->log('order_edit@ome', $order_id, '[退款编辑订单]进入自动审单');
                }else{

                    # 更改订单备注为<待审核> ------
                    $memo = [];
                    $oldmemo= unserialize($orderInfo['mark_text']);
                    $editMemoFlag = true; //用于：如果之前标注了，就不重复标注
                    if ($oldmemo){
                        foreach($oldmemo as $k=>$v){
                            if(is_array($v)){
                                if(in_array('待审核', $v)){
                                    $editMemoFlag = false;
                                }
                            }else{
                                if(strpos($v, '待审核') !== false){
                                    $editMemoFlag = false;
                                }
                            }
                            $memo[] = $v;
                        }
                    }
                    if($editMemoFlag){
                        $newmemo = array('op_name'=>'system', 'op_time'=>date('Y-m-d H:i:s',time()), 'op_content'=>'待审核');
                        $memo[] = $newmemo;
                        $mark_text = serialize($memo);
                        app::get('ome')->model('orders')->update(['mark_text'=>$mark_text],['order_id'=>$order_id]);
                    }

                    # 更改订单标记 ------
                    app::get('ome')->model('orders')->update(['auto_status'=>$orderInfo['auto_status'] | omeauto_auto_const::__ONE_BN_MANY_COLOR_CODE],['order_id'=>$order_id]);
                    $this->log('order_edit@ome', $order_id, '[退款编辑订单]不进入自动审单，因同一基础物料有多个color');
                }
            }
        }

        # 提交事务 ------
        $db->commit($trans);

        return true;
    }

    private function is_one_bn_match_many_color($order_id){

        $return = false;
        if($this->editorderDeletedBns){
            $order_items_color_list = app::get('ome')->model('order_items_color')->getList('bn, color_info',['order_id'=>$order_id, 'bn'=>$this->editorderDeletedBns]);
            $color_infos = [];
            foreach ($order_items_color_list as $value){
                if(!isset($color_infos[$value['bn']])){
                    $color_infos[$value['bn']] = [];
                }
                $color_infos[$value['bn']][$value['color_info']] = $value['color_info'];
                if(count($color_infos[$value['bn']])>1){ //一个bn对应了多个调色color
                    $return = true;
                }
            }
        }
        kernel::single('base_customlog')->log(['$order_id'=>$order_id, 'editorderDeletedBns'=>$this->editorderDeletedBns, '$order_items_color_list'=>$order_items_color_list, '$color_infos'=>$color_infos, '$return'=>$return,], "aftersalev2/is_one_bn_match_many_color");

        return $return;
    }

    public function setOrderBoolTypeDeleteItemFail($order_id){
        $orderObj = app::get('ome')->model('orders');
        $orderInfo = $orderObj->dump(array('order_id'=>$order_id), 'order_bool_type');
        if (!($orderInfo['order_bool_type'] & ome_order_bool_type::__DELETE_ITEM_FAIL_CODE)) {
            $orderBoolType = $orderInfo['order_bool_type'] | ome_order_bool_type::__DELETE_ITEM_FAIL_CODE;
            $orderObj->update(array('order_bool_type'=>$orderBoolType), array('order_id'=>$order_id));
        }
    }

    /**
     * 删除订单商品
     * @param $order_id
     * @param $sdf
     */
    private function deleteOrderItems($order_id, $sdf, &$error_msg){

        $basicMStockLib = kernel::single('material_basic_material_stock');
        $basicMStockFreezeLib = kernel::single('material_basic_material_stock_freeze');
        $db = kernel::database();
        $orderObj = app::get('ome')->model('orders');
        $refundItemList = $sdf['refund_item_list'];
        if(empty($refundItemList) || !is_array($refundItemList)) {
            $error_msg .= "退款明细为空".json_encode($refundItemList);
            $this->log('order_edit@ome', $order_id, $error_msg);
            return false;
        }

        foreach ($refundItemList as $sdf_item){
            //$order_object = app::get('ome')->model('order_objects')->dump(['order_id'=>$order_id,'oid'=>$sdf_item['oid']],'obj_id,obj_type,bn,delete,quantity,cost_avg,divide_order_fee');

            $oid = $sdf_item['oid'];
            $bn = $sdf_item['outer_id'];
            $order_object = kernel::database()->selectrow("select * from sdb_ome_order_objects where order_id = $order_id and oid = '$oid'");

            # 验证：oms是否存在对应的订单obj数据 ------
            if (empty($order_object)){
                $error_msg .= '[退款编辑订单]删除商品bn：'. $sdf_item['bn'] .'失败,订单子订单不存在:'.$sdf_item['oid'];
                $this->log('order_edit@ome', $order_id, $error_msg);
                $this->setOrderBoolTypeDeleteItemFail($order_id);
                continue;
            }

            # 验证：是否已经删除 ------
            if ($order_object['delete'] == 'true'){
                $error_msg .= '[退款编辑订单]删除商品bn：'. $sdf_item['bn'] .'失败,订单商品已经是删除状态,请勿重复操作';
                $this->log('order_edit@ome', $order_id, $error_msg);
                continue;
            }

            # 验证：是否退货商品已经发货 ------
            $get_item_sql = "SELECT item_id,bn FROM sdb_ome_order_items WHERE order_id=" . $order_id . " AND obj_id=" . $order_object['obj_id'] . " AND sendnum>0";
            $is_sended = $db->selectrow($get_item_sql);
            if($is_sended){
                $error_msg .= '[退款编辑订单]退款商品bn：'. $sdf_item['bn'] .'已经存在发货,无法自动删除!';
                $this->log('order_edit@ome', $order_id, $error_msg);
                continue;
            }

            $get_item_sql = "SELECT sum(cost_avg*nums) as item_price, GROUP_CONCAT(bn) bnsStr FROM sdb_ome_order_items WHERE order_id=" . $order_id . " AND obj_id=" . $order_object['obj_id'];
            $itemCostavg = $db->selectrow($get_item_sql);
            # 金额判断 ------
            # 退款金额和订单进行可以相差1元
            if( abs($sdf['refund_fee'] - $order_object['divide_order_fee']) > 1 && abs($sdf['refund_fee']-$itemCostavg['item_price']) > 1 ){
                $error_msg .= '[退款编辑订单]退款商品bn：'. $sdf_item['bn'] .'和订单商品金额不相等('.$sdf['refund_fee'].','.$order_object['divide_order_fee'].'),无法自动删除!';
                $this->log('order_edit@ome', $order_id, $error_msg);
                $this->setOrderBoolTypeDeleteItemFail($order_id);
                continue;
            }

            //删除订单object层商品
            $delete_sql = "UPDATE sdb_ome_order_objects SET `delete`='true' WHERE order_id=". $order_id ." AND obj_id=". $order_object['obj_id'];
            $affect_row = $db->exec($delete_sql);
            if ($affect_row) {

                //删除item层货品
                $delete_sql = "UPDATE sdb_ome_order_items SET `delete`='true' WHERE order_id=" . $order_id . " AND obj_id=" . $order_object['obj_id'];
                $affect_row = $db->exec($delete_sql);
                $msg = $affect_row ? '成功' : '失败';
                $this->log('order_edit@ome', $order_id, '[退款编辑订单]退款完成时删除订单商品:'.$order_object['bn'].$msg);

                $order_sql = "UPDATE sdb_ome_orders SET pmt_goods=pmt_goods-".$order_object['pmt_price'].", pmt_order=pmt_order-".$order_object['part_mjz_discount'].", cost_item=cost_item-".$order_object['amount'];
                $order_sql .= ", total_amount=total_amount-".$sdf['refund_fee'].", final_amount=final_amount-".$sdf['refund_fee']." WHERE order_id=". $order_id;
                kernel::single('base_customlog')->log("更新订单金额sql:".$order_sql, "aftersalev2/order_edit@ome");
                $orderObj->db->exec($order_sql);

                //释放预占
                $sql = "SELECT product_id,bn,nums FROM sdb_ome_order_items WHERE order_id=" . $order_id . " AND obj_id=" . $order_object['obj_id'];
                $itemList = $db->select($sql);
                foreach ($itemList as $item){
                    $result = $basicMStockFreezeLib->unfreeze($item['product_id'], material_basic_material_stock_freeze::__ORDER, 0, $order_id, '', material_basic_material_stock_freeze::__SHARE_STORE, abs($item['nums']));
                    kernel::single('base_customlog')->log($item['bn']."预占流水释放结果:".$result, "aftersalev2/order_edit@ome");
                    if($result){
                        $result2 = $basicMStockLib->unfreeze($item['product_id'], abs($item['nums']));
                        $this->log('order_edit@ome', $order_id, '[退款编辑订单]释放预占成功'.$result2.':'.$order_object['bn']);
                    }
                }

                // custom 根据子订单id查询是否有关联赠品
                if($affect_row) $this->__deletePlatformGift($sdf_item['oid'], $order_id);

            }else{
                return false;
            }

            # 如果一个bn多个调色，就不自动审单(原因：调色是) ------
            if($itemCostavg['bnsStr']){
                $bns = explode(',', $itemCostavg['bnsStr']);
                if(!empty($bns)){
                    $this->editorderDeletedBns = array_merge($this->editorderDeletedBns, $bns);
                }
            }
        }


        return true;
    }

    /**
     * 记录
     */
    private function log($path, $order_id, $info){
        $logObj = app::get('ome')->model('operation_log');
        $logObj->write_log($path, $order_id, $info);
        kernel::single('base_customlog')->log($info, "aftersalev2/$path");
    }

    private function _checkIsFullRefund($orderId)
    {
        kernel::single('ome_delivery_intercept_log')->checkRefundInterceptSuccDelivery($orderId);
    }

    private function _refundSdfToData($sdf) {
        $data = array(
            'refund_bn'     => $sdf['refund_bn'],
            'shop_id'       => $sdf['shop_id'],
            'order_id'      => $sdf['order']['order_id'],
            'currency'      => 'CNY',
            'money'         => $sdf['refund_fee'],
            'cur_money'     => $sdf['cur_money'] ? $sdf['cur_money'] : $sdf['refund_fee'],
            'pay_type'      => $sdf['pay_type'],
            'download_time' => time(),
            'status'        => 'succ',
            'memo'          => $sdf['reason'],
            'trade_no'      => $sdf['alipay_no'],
            'modifiey'      => $sdf['modified'],
            'payment'       => $sdf['payment'],
            't_ready'       => $sdf['t_ready'] ? $sdf['t_ready'] : $sdf['t_sent'],
            't_sent'        => $sdf['t_sent'] ? $sdf['t_sent'] : $sdf['t_ready'],
            't_received'    => $sdf['t_received']
        );
        return $data;
    }

    private function _updateOrderPayed($orderId, $money) {
        if (empty($orderId) || !$money) {
            return false;
        }
        $sql ="update sdb_ome_orders set payed=IF((CAST(payed AS char)-IFNULL(0,cost_payment)-".$money.")>=0,payed-IFNULL(0,cost_payment)-".$money.",0)  where order_id=".$orderId;
        kernel::database()->exec($sql);
        //更新订单支付状态
        if (kernel::single('ome_order_func')->update_order_pay_status($orderId)){
            // $this->_checkAbnormal($orderId);
            return true;
        }else{
            return false;
        }
    }

    protected function _checkAbnormal($orderId){
        if (empty($orderId)) {
            return false;
        }
        $orderObj = app::get('ome')->model('orders');
        $orderData = $orderObj->getList('pay_status,ship_status', array('order_id'=>$orderId), 0, 1);
        $tgOrder = $orderData[0];
        #未发货、部分发货，暂停处理
        if($tgOrder['pay_status'] == '4' && ($tgOrder['ship_status'] == '0' || $tgOrder['ship_status']== '2')){
            $tmp = array();
            //如果是部分退款订单,添加部分退款异常并暂停订单
            $abnormalObj = app::get('ome')->model('abnormal');
            $abnormalTypeObj = app::get('ome')->model('abnormal_type');
            $abnormalTypeInfo = $abnormalTypeObj->getList('type_id,type_name', array('type_name'=>'订单未发货部分退款'), 0, 1);
            if($abnormalTypeInfo){
                $tmp['abnormal_type_id'] = $abnormalTypeInfo[0]['type_id'];
            }else{
                $add_arr['type_name'] = '订单未发货部分退款';
                $abnormalTypeObj->insert($add_arr);
                $tmp['abnormal_type_id'] = $add_arr['type_id'];
            }
            $abnormalInfo = $abnormalObj->getList('abnormal_id,abnormal_memo', array('order_id'=>$orderId), 0, 1);
            $memo = '';
            if($abnormalInfo){
                $tmp['abnormal_id'] = $abnormalInfo[0]['abnormal_id'];
                $oldmemo= unserialize($abnormalInfo[0]['abnormal_memo']);
                if ($oldmemo){
                    foreach($oldmemo as $k=>$v){
                        $memo[] = $v;
                    }
                }
            }
            $op_name = 'system';
            $newmemo =  '订单未发货部分退款，系统自动设置为异常并暂停。';
            $memo[] = array('op_name'=>$op_name, 'op_time'=>date('Y-m-d H:i',time()), 'op_content'=>$newmemo);
            $tmp['abnormal_memo'] = serialize($memo);
            $tmp['abnormal_type_name'] ='订单未发货部分退款';
            $tmp['is_done'] = 'false';
            $tmp['order_id'] = $orderId;
            if($tmp['abnormal_id']) {
                $abnormalObj->update($tmp, array('abnormal_id' => $tmp['abnormal_id']));
            } else {
                $abnormalObj->insert($tmp);
            }
            //如果已经有发货单的撤销相关发货单
            $orderObj->cancel_delivery($orderId);
            //订单暂停并设置为异常
            // $orderObj->update(array('abnormal'=>'true','pause'=>'true'), array('order_id'=>$orderId));
        }
    }

    private function _dealReturnProduct($sdf) {
        $modelReturnProduct = app::get('ome')->model('return_product');
        if($sdf['return_product']) {
            if($sdf['branch_id']) {
                $branchInfo = app::get('ome')->model('branch')->dump(array('branch_id'=>$sdf['branch_id']), 'wms_id');
                if ($sdf['status'] == 5 && $branchInfo['wms_id'] == CN_BRANCH_ID && $sdf['reship']['is_check']>0) {
                    return array('rsp'=>'fail', '菜鸟仓退货单非未审核状态，不处理拒绝状态');
                }
            }
            $idBn = array(
                'return_id' => $sdf['return_product']['return_id'],
                'return_bn' => $sdf['return_product']['return_bn']
            );
            $this->_dealTableAdditional($sdf['table_additional'], $idBn);
            $this->_returnProductUpdateStatus($sdf);
            $msg = '更新成功';
        } else {
            $insertData = $this->_returnProductSdfToData($sdf);
            $returnProductItems = $insertData['return_product_items'];
            unset($insertData['return_product_items']);
            $rs = $modelReturnProduct->insert($insertData);
            if(!$rs) {
                return array('rsp'=>'fail', 'msg'=>'售后申请单新建失败');
            }
            $this->_insertReturnProductItems($returnProductItems, $insertData['return_id']);
            app::get('ome')->model('operation_log')->write_log('return@ome',$insertData['return_id'],'创建售后申请单');
            $msg = '创建售后申请单成功';
            $idBn = array(
                'return_id' => $insertData['return_id'],
                'return_bn' => $insertData['return_bn']
            );
            $this->_dealTableAdditional($sdf['table_additional'], $idBn);
            if (in_array($sdf['status'],array('3','5','6'))) {
                $sdf['return_product'] = $insertData;
                $this->_returnProductUpdateStatus($sdf);
            } elseif($sdf['status'] == '4') {
                return array('rsp'=>'fail', 'msg'=>'退货单未生成，不能完成');
            }
            return $this->_dealAutoReship($sdf, $idBn);
        }
        return array('rsp'=>'succ', 'msg' => $msg);
    }

    private function _returnProductSdfToData($sdf) {
        $opInfo = kernel::single('ome_func')->get_system();
        $data = array(
            'return_bn'  => $sdf['refund_bn'],
            'shop_id'    => $sdf['shop_id'],
            'member_id'  => $sdf['member_id'],
            'order_id'   => $sdf['order']['order_id'],
            'title'      => $sdf['order_bn'].'售后申请单',
            'content'    => $sdf['reason'],
            'comment'    => $sdf['desc'],
            'add_time'   => $sdf['created'],
            'status'     => '1',
            'op_id'      => $opInfo['op_id'],
            'refundmoney'=> $sdf['refund_fee'],
            'money'      => $sdf['refund_fee'],
            'shipping_type'=> $sdf['shipping_type'],
            'source'     => 'matrix',
            'shop_type'  => $sdf['shop_type'],
            'outer_lastmodify'=>$sdf['modified'],
            'delivery_id'=> $sdf['delivery_id'],
            'memo'      =>  $sdf['memo'],
        );
        $isFail = 'false';
        foreach($sdf['refund_item_list'] as $val) {
            $data['return_product_items'][] = array(
                'product_id' => $val['product_id'] ? $val['product_id'] : 0,
                'bn'         => $val['bn'],
                'name'       => $val['title'] ? $val['title']: $val['name'],
                'num'        => $val['num'],
                'price'      => $val['price'],
                'branch_id'   =>$sdf['branch_id'],
            );
            if(empty($val['product_id'])) {
                $isFail = 'true';
            }
        }
        $data['is_fail'] = $isFail;
        return $data;
    }

    private function _insertReturnProductItems($returnProductItems, $returnId) {
        if(empty($returnId) || empty($returnProductItems)) {
            return false;
        }
        foreach($returnProductItems as &$val) {
            $val['return_id'] = $returnId;
        }
        $modelItem = app::get('ome')->model('return_product_items');
        $sql = ome_func::get_insert_sql($modelItem, $returnProductItems);
        $rs = $modelItem->db->exec($sql);
        return $rs['rs'];
    }

    /**
     * 删除售后item； 同时清除在退数量
     */
    private function deleteItemAndReshippingnum($sdf){

        $modelReturnProduct = app::get('ome')->model('return_product');
        $modelReturnProductItems = app::get('ome')->model('return_product_items');
        $modelDlyItems = app::get('ome')->model('delivery_items');

        /* 新增逻辑：要清理在退数量 reshipping_num ------*/

        if(!empty($sdf['refund_bn'])){

            // 售后明细列表item
            $returnItems = $modelReturnProductItems->getList('*', ['return_id'=>$sdf['return_product']['return_id']]);
            if(!empty($returnItems)){

                // 获得快递id
                $returnProduct = $modelReturnProduct->dump(['return_bn'=>$sdf['refund_bn']]);
                if(!empty($returnProduct['delivery_id'])) {

                    // 根据快递id和产品id，逐个清除reshipping_num的值
                    foreach ($returnItems as $item){
                        $dlyItemWhere = ['delivery_id'=>$returnProduct['delivery_id'], 'product_id'=>$item['product_id']];

                        // 查出原有在退数量
                        $dlyItemsOld = $modelDlyItems->dump($dlyItemWhere);
                        if(!empty($dlyItemsOld)){
                            if($dlyItemsOld['reshipping_num'] >= $item['num']){
                                // 新的在退数量 = 原有在退数量 - 本次要删除的售后数量
                                $dlyItemsNewNum = $dlyItemsOld['reshipping_num'] - $item['num'];
                                $modelDlyItems->update(['reshipping_num'=>$dlyItemsNewNum], $dlyItemWhere);   // 将reshipping_num重置
                            }
                        }
                    }
                }
            }
        }

        /* 删除售后item数据---------------------------*/
        $modelReturnProductItems->delete(array('return_id'=>$sdf['return_product']['return_id']));
    }


    private function _returnProductUpdateStatus($sdf) {
        $operateLog = app::get('ome')->model('operation_log');
        $modelReturnProduct = app::get('ome')->model('return_product');
        $returnProduct = $sdf['return_product'];
        switch($sdf['status']) {
            case '1':
                $upData = $this->_returnProductSdfToData($sdf);
                $returnProductItems = $upData['return_product_items'];
                unset($upData['return_product_items']);
                $modelReturnProduct->update($upData, array('return_id'=>$returnProduct['return_id']));

                // 删除售后item； 同时清除在退数量
                $this->deleteItemAndReshippingnum($sdf);

                $this->_insertReturnProductItems($returnProductItems, $returnProduct['return_id']);
                $operateLog->write_log('return@ome', $returnProduct['return_id'], '退款原因、金额或版本变化，重置售后申请单');
                if($sdf['reship']) {
                    $this->cleanReturnStatus($sdf['reship']);
                }
                break;
            case '3':
                $data = array(
                    'status'    => $sdf['status'],
                    'return_id' => $returnProduct['return_id'],
                    'outer_lastmodify' => $sdf['modified'],
                    'choose_type_flag' => $sdf['choose_type_flag']
                );
                $modelReturnProduct->tosave($data, true);
                break;
            case '4':
                if ($sdf['reship'] && !$sdf['reship']['return_logi_name'] && !$sdf['reship']['return_logi_no']) {
                    $this->_updateReshipLogistics($sdf);
                }
                $operateLog->write_log('return@ome', $returnProduct['return_id'],'线上已完成,请进行收货/质检等操作');
                break;
            case '5':
                $data = array(
                    'status'    => $sdf['status'],
                    'return_id' => $returnProduct['return_id'],
                    'outer_lastmodify' => $sdf['modified'],
                );
                $modelReturnProduct->tosave($data, true);
                // 同步拒绝退货单
                if ($sdf['reship']){
                    $reship = $sdf['reship'];
                    app::get('ome')->model('reship')->update(array('is_check'=>'5','t_end'=>time()),array('reship_id'=>$reship['reship_id']));

                    // ----start---- 20230104 前端取消退货单时，ome_delivery_items退回reshipping_num数量的逻辑

                    // 取出退货单的退货物料详情
                    $reshipItemsList = app::get('ome')->model('reship_items')->getList('*',['reship_id'=>$reship['reship_id']]);
                    if(!$reshipItemsList){
                        $operateLog->write_log('reship@ome',$reship['reship_id'],'前端拒绝时修改发货单底单子表reshipping_num失败，查不到退货单子单');
                    }

                    // 退回底单明细的 在退数量 (reshipping_num)
                    $omeDlyItemModel = app::get('ome')->model('delivery_items');
                    foreach ($reshipItemsList as $item){

                        if(!is_numeric($item['ome_dly_item_id']) || $item['ome_dly_item_id']<0){
                            $operateLog->write_log('reship@ome',$reship['reship_id'],"前端拒绝时修改发货单底单子表reshipping_num失败，查不到底单发货单明细【{$item['item_id']}】-【{$item['bn']}】");
                        }

                        // ----- start 取消发货单时，更新底单的退货数预占
                        $omeItemInfo = $omeDlyItemModel->dump(['item_id'=>$item['ome_dly_item_id']],'reshipping_num');
                        $newReshippingNum = $omeItemInfo['reshipping_num'] - $item['num'];  // 退回reshipping_num
                        if($newReshippingNum<0) $newReshippingNum=0;

                        $upOmeDlyItemDate = [
                            'reshipping_num'=>$newReshippingNum
                        ];

                        if(!$omeDlyItemModel->update($upOmeDlyItemDate,['item_id'=>$item['ome_dly_item_id']])){
                            $operateLog->write_log('reship@ome',$reship['reship_id'],"前端拒绝时修改发货单底单子表reshipping_num失败，退回物料时失败【{$item['item_id']}】-【{$item['bn']}】");
                        }
                        // ----- end

                    }

                    // ----end---- 20230104

                    $operateLog->write_log('reship@ome',$reship['reship_id'],'前端拒绝完成');
                }
                break;
            case '6':
                $this->_updateReshipLogistics($sdf);
                break;
            default:
                break;
        }
    }

    private function _updateReturnProductLogistics($returnId, $logisticsCompany, $logisticsNo) {
        if($returnId && $logisticsCompany && $logisticsNo) {
            $logisticsInfo = array(
                'shipcompany' => $logisticsCompany,
                'logino' => $logisticsNo,
            );
            $rData = array(
                'process_data' => serialize($logisticsInfo)
            );
            $rs = app::get('ome')->model('return_product')->update($rData, array('return_id' => $returnId));
            $memo = '更新物流公司:' . $logisticsCompany . ',物流单号:' . $logisticsNo;
            $operateLog = app::get('ome')->model('operation_log');
            $operateLog->write_log('return@ome', $returnId, $memo);
            return $rs;
        }
        return false;
    }

    private function _updateReshipLogistics($sdf){
        $reship = $sdf['reship'];
        $logisticsCompany = $sdf['logistics_company'];
        $logisticsNo = $sdf['logistics_no'];
        if ($reship && $logisticsCompany && $logisticsNo) {
            $memo ='更新物流公司:'.$logisticsCompany.',物流单号:'.$logisticsNo;
            $upData = array(
                'return_logi_name'=>$logisticsCompany,
                'return_logi_no'=>$logisticsNo,
                'outer_lastmodify'=>$sdf['modified'],
            );
            $rs = app::get('ome')->model('reship')->update($upData,array('reship_id'=>$reship['reship_id']));
            $operateLog = app::get('ome')->model('operation_log');
            $operateLog->write_log('reship@ome',$reship['reship_id'],$memo);
            $this->_updateReturnProductLogistics($reship['return_id'], $logisticsCompany, $logisticsNo);
            return $rs;
        }
        return false;
    }
    private function _dealAutoReship($sdf, $idBn) {

        $modelOrders = app::get('ome')->model('orders');
        $operateLog = app::get('ome')->model('operation_log');
        $modelReturnProduct = app::get('ome')->model('return_product');
        $returnProduct = $modelReturnProduct->dump(['return_bn'=>$idBn['return_bn']]);
        if ($returnProduct) {
            $sdf['return_product'] = [
                'return_id' => $returnProduct['return_id'],
                'delivery_id' => $returnProduct['delivery_id'],
                'status' => $returnProduct['status'],
                'money' => $returnProduct['refundmoney'],
                'is_fail' => $returnProduct['is_fail'],
            ];
        }
        $insertData = $this->_reshipSdfToData($sdf);
        $order = $modelOrders->dump($insertData['order_id']);
        $order_id = $order['order_id'];
        $modelOrderItems = app::get('ome')->model('order_items');
        $returnItems = [];
        foreach($insertData['reship_items'] as $reshipItem){
            $orderItem = $modelOrderItems->dump(['order_id'=>$order_id, 'product_id'=>$reshipItem['product_id']]);
            $returnItems[$order_id.'_'.$orderItem['item_id']] = [
                "selected"      => "true",
			    "item_name"     => $reshipItem['product_name'],
			    "item_price"    => $reshipItem['price'],
			    "return_num"    => $reshipItem['num'],
            ];
        }
        $postParams = [
            'reship_id' =>  "" ,
            'reship_bn' =>  $insertData['reship_bn'] ,
            'source'    =>  $insertData['source'] ,
            'return_id' =>  $insertData['return_id'] ,
            'return_type'   =>  "return" ,
            'supplier'  =>  $order['order_bn'] ,
            'order_id'  =>  $order['order_id'] ,
            'is_protect'    =>  $insertData['is_protect'] ,
            'shop_id'   =>  $order['shop_id'] ,
            'member_id' =>  $insertData['member_id'] ,
            'return_logi_name'  =>  $insertData['return_logi_name'] ,
            'logi_name' =>  $insertData['return_logi_name'] ,
            'logi_id'   =>  $insertData['logi_id'] ,
            'logi_no'   =>  $insertData['return_logi_no'] ,
            'return_logi_no'    =>  $insertData['return_logi_no'] ,
            'ship_name' =>  $insertData['ship_name'],
            'ship_area' =>  $insertData['ship_area'] ,
            'ship_addr' =>  $insertData['ship_addr'] ,
            'ship_zip'  =>  $insertData['ship_zip'] ,
            'ship_tel'  =>  $insertData['ship_tel'] ,
            'ship_email'    =>  $insertData['ship_email'] ,
            'ship_mobile'   =>  $insertData['ship_mobile'] ,
            'delivery_type' =>  $insertData['delivery_type'] ,
            'branch_id' =>  $insertData['branch_id'] ,
            'memo'  =>  "" ,
            'return_item' => $returnItems,
        ];
        // 统一类库声明
        $reshipModel = app::get('ome')->model('reship');
        $oShop = app::get('ome')->model('shop');
        $rChangeObj = kernel::single('ome_return_rchange');
        $omeDeliveryReturnObj = kernel::single('ome_delivery_return');
        // 开始执行新增退货单逻辑
        $reship_ids = [];
        // 开始新建退货单的逻辑
        // 内部错误信息变量
        $errorMsg='';
        // 订单号
        $orderId = $postParams['order_id'];
        // 确认勾选退货物料的发货单
        foreach($postParams['return_item'] as $key=>$itemInfo){
            // 没勾选的unset掉
            if(!isset($itemInfo['selected']) || $itemInfo['selected']!=='true' || $itemInfo['return_num']<=0){
                unset($postParams['return_item'][$key]);
                continue;
            }
            // 报错信息初始化
            $errorMsgBase = "退货预处理失败，【{$itemInfo['item_name']}】时出错.原因：";

            list($orderObjId,$orderItemId) = explode('_',$key);

            // 根据勾选物料确认退货的发货单
            $result = $omeDeliveryReturnObj->getDlyInfoByProduct($orderItemId,$orderId,$itemInfo['return_num'],$errorMsg);
            if( $result === false ) return array('rsp'=>'succ', 'msg'=>$errorMsgBase.$errorMsg);
            if( empty($result) ) return array('rsp'=>'succ', 'msg'=>"【{$itemInfo['item_name']}】底单明细可退数非法");

            // 组装退货单及明细
            foreach ($result as $omeDlyId => $returnItemInfo){
                $postParams['delivery'][$omeDlyId][] = $returnItemInfo;
            }
        }

        if(!isset($postParams['delivery']) || empty($postParams['delivery'])) return array('rsp'=>'succ', 'msg'=>'请选择退货商品');

        $errorMsgBase = "";      // 基础报错信息

        $shopType = $oShop->getShoptype($postParams['shop_id']);

        // 初始化退货单的主信息拼装
        $insertData = [
            'reship_id' => $postParams['reship_id'],
            'reship_bn' => $postParams['reship_bn'],
            'source'    => $postParams['source'],
            'return_type'    => $postParams['return_type'],
            'order_id'    => $postParams['order_id'],
            'is_protect'    => $postParams['is_protect'],
            'shop_id'    => $postParams['shop_id'],
            'shop_type'    => $shopType,
            'member_id' =>  $postParams['member_id'],
            'return_logi_name' =>  $postParams['return_logi_name'],
            'return_logi_no' =>  $postParams['return_logi_no'],
            'logi_no' =>  $postParams['logi_no'],
            'logi_id' =>  $postParams['logi_id'],
            'logi_name' =>  $postParams['logi_name'],
            'ship_name' =>  $postParams['ship_name'],
            'ship_area' =>  $postParams['ship_area'],
            'ship_addr' =>  $postParams['ship_addr'],
            'ship_zip' =>  $postParams['ship_zip'],
            'ship_tel' =>  $postParams['ship_tel'],
            'ship_email' =>  $postParams['ship_email'],
            'ship_mobile' =>  $postParams['ship_mobile'],
            'return_id' =>  $postParams['return_id'],
            'memo' =>  $postParams['memo'],
        ];

        // 开始组装发货单和发货单明细
        foreach($postParams['delivery'] as $omeDlyId=>$dlyItemList){

            $totalMoney = 0;
            $insertData['return'] = []; // 初花退货单的明细

            foreach ($dlyItemList as $returnProductValue){

                // 报错信息初始化
                $errorMsgBase = "建退货单失败，【{$itemInfo['item_name']}】时出错.原因:";
                // 确认发货单 完善退货单的主信息
                $insertData['notice_delivery_bn'] = $returnProductValue['notice_delivery_bn'];       // 通知单号
                $insertData['ome_delivery_bn'] = $returnProductValue['ome_delivery_bn'];      // 底单号
                $insertData['branch_type'] = $returnProductValue['branch_type'];              // 退货仓类型  很重要，关系到发票逻辑
                $insertData['branch_id'] = $returnProductValue['branch_id'];                  // 发货仓
                $insertData['delivery_cate'] = $returnProductValue['delivery_cate'];          // 退货单店铺物料类型

                // 确认发货单的退货物料明细
                $lineAmount =  bcmul($returnProductValue['return_product_price'],$returnProductValue['return_product_num'],3); // 计算行金额
                $returnProduct = [
                    'goods_name'=>$returnProductValue['return_product_name'],
                    'product_id'=>$returnProductValue['return_product_id'],
                    'goods_bn'=>$returnProductValue['return_product_bn'],
                    'price'=>$returnProductValue['return_product_price'],
                    'num'=>$returnProductValue['return_product_num'],
                    'branch_id'=>$returnProductValue['branch_id'],
                    'sap_ind'=>$returnProductValue['delivery_sap_ind'],
                    'sap_sku'=>$returnProductValue['delivery_sap_sku'],
                    'amount'=>$lineAmount,
                    'ome_dly_item_id'=>$returnProductValue['ome_delivery_items_id'],
                    'order_item_id'=>$returnProductValue['order_item_id'],
                ];

                $insertData['return'][] = $returnProduct;

                $totalMoney = bcadd($lineAmount,$totalMoney,3);

            }

            $insertData['bmoney'] = 0;  // 其他费用
            $insertData['tmoney'] = $totalMoney;  // 退款费用
            $insertData['totalmoney'] = $totalMoney;  // 最后合计金额

            // 如果是换货发货的单子，重组换货的商品数据
            if($insertData['change']){
                $insertData = $rChangeObj->format_rchange_data($insertData);
            }

            // 换货需验证退入和换出的仓库
            if($insertData["return_type"] == "change"){
                $result_branch_check = kernel::single('o2o_return')->check_reship_branch($insertData["branch_id"],$insertData["changebranch_id"],$errorMsg);
                if(!$result_branch_check) return array('rsp'=>'succ', 'msg'=>$errorMsgBase.$errorMsg);
            }

            // 数据验证(商品、库存等)
            if(!$reshipModel->validate($insertData,$errorMsg)){
                return array('rsp'=>'succ', 'msg'=>$errorMsgBase.$errorMsg);
            }

            // 新增生成退换货
            $msg='';
            $reshipId = 0;
            // print_r($insertData);exit;
            $branch_mapping = app::get('ome')->model('branch_mapping')->dump(['delivery_branch_id'=>$insertData['branch_id']]);
            if ($branch_mapping) {
                $insertData['branch_id'] = $branch_mapping['refund_branch_id'];
            }
            $reship_bn = $reshipModel->create_treship($insertData,$msg,$reshipId);
            // var_dump($insertData);exit;

            if($reship_bn == false) {
                return array('rsp'=>'succ', 'msg'=>$msg);
            }

            if($reshipId<=0){
                //新增直接审核需要获取新的reship_id js这块需要直接跳转save_check审核方法
                $reshipInfo = $reshipModel->getList('reship_id',array('reship_bn'=>$reship_bn),0,1);
                $reshipId = $reshipInfo[0]['reship_id'];
            }

            $reship_ids[] = $reshipId;
        }
        $msg = '新建退货单';
        return array('rsp'=>'succ', 'msg'=>$msg);
    }
    private function _dealReship($sdf) {

        //return array('rsp'=>'succ', 'msg'=>'退货单同步不开启');

        $msg = '退货单同步开启后, 仅处理退回物流信息;';
        if($sdf['reship']) {
            if(empty($sdf['logistics_no'])){
                $msg .= '无退回物流单号';
            }else{
                $msg .= '物流单号:'.$sdf['logistics_no'];
            }
            if ($sdf['reship']['shipcompany'] != $sdf['logistics_company'] || $sdf['reship']['logino'] != $sdf['logistics_no']) {
                if ($this->_updateReshipLogistics($sdf)) {
                    $msg .= '更新退回物流信息成功';
                }else{
                    $msg .= '更新退回物流信息失败';
                }
            }else{
                $msg .= '退回物流信息有误';
            }
        }else{
            $msg .= '无退回物流信息';
        }
        return array('rsp'=>'succ', 'msg'=>$msg);

        $modelReship = app::get('ome')->model('reship');
        $operateLog = app::get('ome')->model('operation_log');
        if($sdf['reship']) {
            $msg = '仅处理未审核、拒绝状态和物流信息';
            if($sdf['reship']['shipcompany'] != $sdf['logistics_company'] || $sdf['reship']['logino'] != $sdf['logistics_no']) {
                if($this->_updateReshipLogistics($sdf)) {
                    $msg = '更新物流信息成功';
                }
            }
            if ($sdf['status'] == '5') {#拒绝
                if ($sdf['reship']['is_check']>0) {
                    return array('rsp'=>'fail', '本地退货单非未审核状态，不处理拒绝状态');
                }else{
                    $rs = $modelReship->update(array('is_check'=>'5'),array('reship_id'=>$sdf['reship']['reship_id'], 'is_check|noequal'=>'5'));
                    if(is_bool($rs)) {
                        $msg = '退货单已经被拒绝';
                    } else {
                        $msg = '退货单更新为拒绝状态';
                        $memo = '状态:拒绝';
                        $operateLog->write_log('reship@ome', $sdf['reship']['reship_id'], $memo);
                        if ($sdf['return_product']) {
                            $returnId = $sdf['return_product']['return_id'];
                            $rpData = array('status' => '5', 'last_modified' => time());
                            app::get('ome')->model('return_product')->update($rpData, array('return_id'=>$returnId));
                            $operateLog->write_log('return@ome', $returnId, $memo);
                        }
                    }
                }
            }
        } else {
            $insertData = $this->_reshipSdfToData($sdf);
            if($insertData['reship_items']) {
                $reshipItems = $insertData['reship_items'];
                unset($insertData['reship_items']);
            }
            $rs = $modelReship->insert($insertData);
            if(!$rs) {
                return array('rsp'=>'succ', 'msg'=>'退货单新建失败');
            }
            $operateLog->write_log('reship@ome',$insertData['reship_id'], '新建退货单');
            $this->_insertReshipItems($reshipItems, $insertData['reship_id']);
            if($sdf['return_product']['status'] < 3) {
                app::get('ome')->model('return_product')->update(array('status' => '3'), array('return_id' => $sdf['return_product']['return_id']));
                $operateLog->write_log('return@ome', $sdf['return_product']['return_id'], '由于退货单下载,售后单不为已接受更新为已接受');
            }
            $this->_updateReturnProductLogistics($sdf['return_product']['return_id'], $sdf['logistics_company'], $sdf['logistics_no']);
            //极速退款打标在扩展表
            if($sdf['jsrefund_flag'] == 'true'){

                $modelReship->db->exec("UPDATE sdb_ome_return_product_tmall set jsrefund_flag='true' WHERE return_bn='".$sdf['return_product']['return_bn']."'");
            }
            $msg = '新建退货单'.$insertData['reship_id'].',单号为:'.$insertData['reship_bn'];
        }
        return array('rsp'=>'succ', 'msg'=>$msg);
    }

    private function _reshipSdfToData($sdf) {
        $tgOrder = $sdf['order'];
        $returnProduct = $sdf['return_product'];
        $opInfo = kernel::single('ome_func')->get_system();
        $data = array(
            'reship_bn'     => $sdf['refund_bn'],
            'shop_id'       => $sdf['shop_id'],
            'order_id'      => $tgOrder['order_id'],
            'delivery_id'   => $returnProduct['delivery_id'],
            'member_id'     => $tgOrder['member_id'],
            'logi_name'     => $sdf['logi_name'],
            'logi_no'       => $tgOrder['logi_no'],
            'logi_id'       => $tgOrder['logi_id'],
            'ship_name'     => $tgOrder['ship_name'],
            'ship_area'     => $tgOrder['ship_area'],
            'delivery'      => $tgOrder['shipping'],
            'ship_addr'     => $tgOrder['ship_addr'],
            'ship_zip'      => $tgOrder['ship_zip'],
            'ship_tel'      => $tgOrder['ship_tel'],
            'ship_email'    => $tgOrder['ship_email'],
            'ship_mobile'   => $tgOrder['ship_mobile'],
            'is_protect'    => $tgOrder['is_protect'],
            'return_id'     => $returnProduct['return_id'],
            'return_logi_name' => $sdf['logistics_company'],
            'return_logi_no' => $sdf['logistics_no'],
            'outer_lastmodify' => $sdf['modified'],
            'source'        => 'matrix',
            't_begin'       => $sdf['created'],
            'op_id'         => $opInfo['op_id'],
            'is_check'      => in_array($sdf['status'], array('0', '5')) ? $sdf['status'] : '0',
            'branch_id'     => $sdf['branch_id'],
        );
        if($sdf['refund_item_list']) {
            foreach ($sdf['refund_item_list'] as $item ) {
                $data['reship_items'][] = array(
                    'op_id'  => $opInfo['op_id'],
                    'bn'     => $item['bn'],
                    'num'    => $item['num'],
                    'price'  => $item['price'],
                    'branch_id' => $sdf['branch_id'],
                    'product_name' => $item['name'],
                    'product_id' => $item['product_id'],
                );

            }
            $data['tmoney'] = $returnProduct['money'];
            $data['totalmoney'] = $returnProduct['money'];//总计应退金额
        }
        return $data;
    }

    private function _insertReshipItems($reshipItems, $reshipId) {
        if(empty($reshipId) || empty($reshipItems)) {
            return false;
        }
        foreach($reshipItems as &$val) {
            $val['reship_id'] = $reshipId;
        }
        $modelItem = app::get('ome')->model('reship_items');
        $sql = ome_func::get_insert_sql($modelItem, $reshipItems);
        $rs = $modelItem->db->exec($sql);
        return $rs['rs'];
    }

    #清空本地状态和已生成单据
    private function cleanReturnStatus($reship){
        $return_id = (int) $reship['return_id'];
        $reship_id = (int) $reship['reship_id'];
        $oReship = app::get('ome')->model('reship');
        $oOperation_log = app::get('ome')->model('operation_log');//写日志
        $oReship->db->exec('DELETE FROM sdb_ome_reship WHERE reship_id='.$reship_id);
        $oReship->db->exec('DELETE FROM sdb_ome_reship_items WHERE reship_id='.$reship_id);
        $oReship->db->exec('DELETE FROM sdb_ome_return_process WHERE reship_id='.$reship_id);
        $oReship->db->exec('DELETE FROM sdb_ome_return_process_items WHERE reship_id='.$reship_id);
        $memo = '退款原因、金额或版本变化,已生成退货单' . $reship['reship_bn'] . '清除';
        $oOperation_log->write_log('return@ome',$return_id,$memo);
    }

    //识别是否天猫开启AG，如果开启的做标记接口请求
    private function _noticeAg($sdf){
        //取当前订单的处理状态
        $orderObj = app::get('ome')->model('orders');
        $order_filter = array("order_id"=>$sdf['order']['order_id']);
        $order_detail = $orderObj->dump($order_filter, 'order_bn,process_status,source');

        $aliag_status = app::get('ome')->getConf('shop.aliag.config.'.$sdf['shop_id']);
        if($aliag_status && $sdf['shop_type'] == 'tmall' && $sdf['status'] == 0 && $order_detail['source'] == 'matrix'){
            //识别是否开启AG并且是天猫订单的新建退款申请
            $params = array(
                'order_bn' => $order_detail['order_bn'],
                'apply_id' => $sdf['apply_id'],
                'refund_bn' => $sdf['refund_apply_bn'],
                'is_aftersale_refund' => false,
                'shop_id' => $sdf['shop_id'],
            );

            //检查当前订单的状态
            if(in_array($order_detail['process_status'],array('unconfirmed','confirmed'))){
                $params['cancel_dly_status'] = 'SUCCESS';
            }else{
                $params['cancel_dly_status'] = 'FAIL';
            }

            kernel::single('ome_service_refund')->refund_request($params);
        }
    }

    /**
     * 删除平台下发赠品
     * @param $oid
     * @param $order_id
     * @return void
     */
    public function __deletePlatformGift($oid, $order_id)
    {
        kernel::single('base_customlog')->log("[__deletePlatformGift] 开始处理oid:".$oid."订单id：".$order_id, "aftersalev2/order_edit@ome");
        $orderObjectObj = app::get('ome')->model('order_objects');
        $orderItemObj = app::get('ome')->model("order_items");

        // 查询销售子订单所关联的赠品子订单
        $sql = "
            select obj_id,main_oid,bn from 
            sdb_ome_order_objects 
            where 1
            and order_id = '$order_id' 
            and FIND_IN_SET('$oid',main_oid) 
            and divide_order_fee=0
            and `delete`='false'
         ";
        kernel::single('base_customlog')->log($sql, "aftersalev2/order_edit@ome");
        $giftOrderObjects = kernel::database()->select($sql);
        kernel::single('base_customlog')->log($giftOrderObjects, "aftersalev2/order_edit@ome");

        if (empty($giftOrderObjects)) {
            return;
        }

        // 不为空,则逐个循环,检查关联销售子订单是否都已删除
        foreach ($giftOrderObjects as $giftOrderObject) {

            // check1 子订单明细已审单,则不处理
            $splitGiftOrderObjectItemFilter = [
                'order_id' => $order_id,
                'obj_id' => $giftOrderObject['obj_id'],
                'sendnum|than' => 0,
            ];
            $splitGiftOrderObjectItemCount = $orderItemObj->count($splitGiftOrderObjectItemFilter);

            if ($splitGiftOrderObjectItemCount > 0) {
                kernel::single('base_customlog')->log("已发货不处理,obj_bn:".$giftOrderObject['bn'], "aftersalev2/order_edit@ome");
                continue;
            }

            // 获取明细
            $giftOrderObjItemsFilter = [
                'order_id' => $order_id,
                'obj_id' => $giftOrderObject['obj_id'],
                'delete' => 'false',
            ];
            $giftOrderObjItems = $orderItemObj->getList('item_id,product_id,nums,bn', $giftOrderObjItemsFilter);

            // 删除子订单层
            $affect_row = $orderObjectObj->db->exec("UPDATE sdb_ome_order_objects SET `delete`='true' WHERE order_id=" . $order_id . " AND obj_id=" . $giftOrderObject['obj_id'] . " AND `delete`='false'");

            if (!$affect_row) {
                // todo 是否要报警
                kernel::single('base_customlog')->log("删除失败,obj_bn:".$giftOrderObject['bn'], "aftersalev2/order_edit@ome");
                continue;
            }

            // 删除订单明细层
            $basicMStockLib = kernel::single('material_basic_material_stock');
            $basicMStockFreezeLib = kernel::single('material_basic_material_stock_freeze');
            foreach ($giftOrderObjItems as $giftOrderObjItem) {
                $affect_row = $orderItemObj->db->exec("UPDATE sdb_ome_order_items set `delete`='true' WHERE order_id=" . $order_id . " AND item_id=" . $giftOrderObjItem['item_id'] . " AND `delete`='false'");
                $msg = $affect_row ? "成功" : "失败";
                $this->log('order_edit@ome', $order_id, '[退款编辑订单]退款完成时删除订单商品对应赠品:'.$giftOrderObject['bn'].$msg);
                if($affect_row){
                    $result = $basicMStockFreezeLib->unfreeze($giftOrderObjItem['product_id'], material_basic_material_stock_freeze::__ORDER, 0, $order_id, '', material_basic_material_stock_freeze::__SHARE_STORE, abs($giftOrderObjItem['nums']));
                    kernel::single('base_customlog')->log($giftOrderObjItem['bn']."预占流水释放结果:".$result, "aftersalev2/order_edit@ome");
                    if($result){
                        $result2 = $basicMStockLib->unfreeze($giftOrderObjItem['product_id'], abs($giftOrderObjItem['nums']));
                        $this->log('order_edit@ome', $order_id, '[退款编辑订单]释放对应赠品预占成功'.$result2.':'.$giftOrderObject['bn']);
                    }
                }

            }

            // 订单金额处理
            $giftOrderObject['part_mjz_discount'] = $giftOrderObject['part_mjz_discount'] ? $giftOrderObject['part_mjz_discount'] : 0;
            $giftOrderObject['divide_order_fee'] = $giftOrderObject['divide_order_fee'] ? $giftOrderObject['divide_order_fee'] : 0;
            $giftOrderObject['amount'] = $giftOrderObject['amount'] ? $giftOrderObject['amount'] : 0;
            $giftOrderObject['pmt_price'] =  $giftOrderObject['pmt_price'] ?  $giftOrderObject['pmt_price'] : 0;

            $sql = "update sdb_ome_orders set pmt_goods=pmt_goods-" . $giftOrderObject['pmt_price'] . ",pmt_order=pmt_order-" . $giftOrderObject['part_mjz_discount'] . ",cost_item=cost_item-" . $giftOrderObject['amount'] . ",total_amount=total_amount-" . $giftOrderObject['divide_order_fee'] . ",final_amount=final_amount-" . $giftOrderObject['divide_order_fee'] . "  where order_id=" . $order_id;
            kernel::single('base_customlog')->log("更新订单金额sql:".$sql, "aftersalev2/order_edit@ome");

            kernel::database()->exec($sql);
        }
    }

    private function _dealMiaozuRefund($sdf) {

        $msg = '创建退款单';
        $refundApply = $sdf['refund_apply'];
        if(!$refundApply) {
            $applyData = $this->_refundApplySdfToData($sdf);
            $applyData['order_type'] = 'miaozu';
            $modelRefundApply = app::get('ome')->model('refund_apply');
            $insertRes = $modelRefundApply->insert($applyData);
            if (!$insertRes) {
                $applyData = $modelRefundApply->db_dump(array('refund_apply_bn' => $sdf['refund_bn']));
            }
            $refundApply = $sdf['refund_apply'] = $applyData;
            kernel::single('base_customlog')->log(['$refundApply'=>$refundApply, '$insertRes'=>$insertRes, '$sdf'=>$sdf], "aftersalev2/_dealRefund");
        }

        if ($refundApply) {
            $filter = array(
                'apply_id' => $refundApply['apply_id'],
            );

            if($sdf['status'] == '4' || $sdf['status'] == 4){
                $updateData = array('status' => '4','refunded' => $sdf['refund_fee']);
                app::get('ome')->model('refund_apply')->update($updateData,$filter);
                app::get('ome')->model('operation_log')->write_log('refund_apply@ome', $refundApply['apply_id'], '退款成功');
                $msg .= "&nbsp;&nbsp;更新退款申请单[{$refundApply['refund_apply_bn']}]为已退款";

                kernel::single('ome_refund_apply')->deleteOrderGoods($refundApply['apply_id'],$sdf['order']['order_id'],$sdf['refund_item_list']);

                if($this->_updateOrderPayed($sdf['order']['order_id'],$sdf['refund_fee'])) {
                    $msg .= '&nbsp;&nbsp;更新订单支付状态';
                }
                // 全额退款 拦截发货单
                $this->_checkIsFullRefund($sdf['order']['order_id'], $refundApply);

            }
            else if($sdf['status'] == '3'){
                $updateData = array('status' => '3');
                app::get('ome')->model('refund_apply')->update($updateData,$filter);
                app::get('ome')->model('operation_log')->write_log('refund_apply@ome', $refundApply['apply_id'], '退款拒绝');
                $msg .= "&nbsp;&nbsp;更新退款申请单[{$refundApply['refund_apply_bn']}]为已拒绝";
                kernel::single('ome_order_func')->update_order_pay_status($sdf['order']['order_id']);
            }
            else{
                kernel::single('ome_order_func')->update_order_pay_status($sdf['order']['order_id']);
                app::get('ome')->model('operation_log')->write_log('refund_apply@ome', $refundApply['apply_id'], '创建退款申请单');
            }
        }



        # 如果 (是部分退款订单，并且退款完成，并且发货状态是未发货或部分发货)，就 {自动对订单做相关处理} ------
        $order_detail = app::get('ome')->model('orders')->dump(array('order_id'=>$sdf['order']['order_id']), '*');
        kernel::single('base_customlog')->log('开始自动编辑订单外部'.json_encode($sdf).json_encode($order_detail), "aftersalev2/out");
        if($order_detail['pay_status'] == '4' && $sdf['status'] == '4' && in_array($order_detail['ship_status'], array('0','2'))) { // 部分退款 && 部分退款状态为完成 && 未发货或部分发货
            $error_msg = '';
            $isResultEdit = $this->_autoEditorder($sdf, $error_msg);
            if(!$isResultEdit){
                $msg .= '&nbsp;&nbsp;'. $error_msg;
            }
        }

        return array('rsp'=>'succ', 'msg'=>$msg.'ccc');
    }
}
