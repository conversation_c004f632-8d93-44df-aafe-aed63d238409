# 发票退款数据查询脚本使用说明

## 功能描述

这个脚本用于查询发票表和操作日志表的关联数据，特别针对"仅退款修改发票为待作废"的操作记录。

## 查询条件

- 操作日志内容包含："仅退款修改发票为待作废：退款单号:{退款单号}"
- 可以通过订单号数组进行精确查询
- 如果不指定订单号，则查询所有符合条件的记录

## 输出字段

1. **订单号** - 来自 `orderinvoice.order_bn`
2. **备注** - 来自 `orderinvoice.remarks`
3. **红票号** - 来自 `orderinvoice.invoice_no_red`
4. **开红时间** - 来自 `orderinvoice.red_dateline`
5. **退款单号** - 从操作日志中通过正则表达式提取

## 使用方法

### 方法1：查询指定订单号

编辑 `script/invoice.php` 文件，在 `$orderBns` 数组中添加要查询的订单号：

```php
$orderBns = array(
    '341093271893103708',
    '341093271893103709',
    // 添加更多订单号...
);
```

### 方法2：查询所有符合条件的记录

保持 `$orderBns` 数组为空：

```php
$orderBns = array();
```

### 执行脚本

```bash
cd /path/to/your/project
php script/invoice.php
```

## 输出文件

脚本会在 `script/` 目录下生成CSV文件，文件名格式为：
`invoice_refund_data_YYYYMMDD_HHMMSS.csv`

文件包含UTF-8 BOM头，确保中文在Excel中正确显示。

## 示例输出

```
开始查询发票退款数据...
找到 15 条符合条件的记录
数据已导出到文件: /path/to/script/invoice_refund_data_20241201_143022.csv
共导出 15 条记录

数据预览（前5条）:
--------------------------------------------------------------------------------
订单号                 红票号            退款单号              开红时间              备注
--------------------------------------------------------------------------------
341093271893103708    <USER>          <GROUP>    2024-12-01 14:30:22    退款处理
341093271893103709    12345679          341093271893103709    2024-12-01 14:25:15    部分退款
```

## 数据库表结构

### custom_invoice_orderinvoice (发票表)
- `order_bn`: 订单号
- `remarks`: 备注
- `invoice_no_red`: 红票号
- `red_dateline`: 开红时间
- `create_time`: 创建时间

### ome_operation_log (操作日志表)
- `obj_id`: 操作对象ID (订单号)
- `obj_type`: 操作对象类型 ('orderinvoice')
- `memo`: 操作备注 (包含退款单号信息)

## 注意事项

1. 确保数据库连接正常
2. 确保有足够的权限访问相关表
3. 脚本会自动处理时区设置（上海时区）
4. 如果查询结果为空，脚本会提示"没有找到符合条件的记录"
5. CSV文件使用UTF-8编码，支持中文显示

## 错误处理

脚本包含完整的错误处理机制：
- 数据库连接错误
- 文件创建错误
- SQL查询错误
- 数据格式错误

如果遇到错误，脚本会显示具体的错误信息并退出。 