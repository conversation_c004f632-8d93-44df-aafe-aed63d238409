#!/usr/bin/env php
<?php

$root_dir = realpath(dirname(__FILE__).'/../');
require_once($root_dir."/config/config.php");
define('APP_DIR',ROOT_DIR."/app/");
require_once(APP_DIR.'/base/kernel.php');
if(!kernel::register_autoload()){
    require(APP_DIR.'/base/autoload.php');
}

require_once(dirname(__FILE__) . '/lib/init.php');
cachemgr::init(false);

// 上海时区
date_default_timezone_set('Asia/Shanghai');

$orderBns = array(
    // '订单号1',
);

// 如果没有指定订单号，则查询所有符合条件的记录
if (empty($orderBns)) {
    echo "未指定订单号，将查询所有符合条件的记录...\n";
}

// 查询发票表和操作日志关联数据
function queryInvoiceWithRefundData($orderBns = array()) {
    $db = kernel::database();
    $result = array();
    
    // 先查询发票表
    $where = "1=1";
    if (!empty($orderBns)) {
        $orderBns_str = "'" . implode("','", $orderBns) . "'";
        $where .= " AND order_bn IN ($orderBns_str)";
    }
    
    $invoice_sql = "SELECT 
                        id,
                        order_bn,
                        remarks,
                        invoice_no_red,
                        red_dateline,
                        create_time
                    FROM sdb_invoice_orderinvoice 
                    WHERE $where
                    ORDER BY create_time DESC";
    
    $invoices = $db->select($invoice_sql);
    
    if (empty($invoices)) {
        return $result;
    }
    
    echo "找到 " . count($invoices) . " 条发票记录，开始查询对应的操作日志...\n";
    
    // 循环查询每个发票对应的操作日志
    foreach ($invoices as $invoice) {
        
        $log_sql = "SELECT memo 
                    FROM sdb_ome_operation_log 
                    WHERE obj_id = '{$invoice['id']}' 
                        AND obj_type = 'orderinvoice@invoice'
                        AND memo LIKE '%仅退款修改发票为待作废：退款单号:%'";
        
        $logs = $db->select($log_sql);
        
        if (!empty($logs)) {
            // 提取退款单号
            $refund_no = '';
            $remarks = '';
            foreach ($logs as $log) {
                if (preg_match('/退款单号:\{([^}]+)\}/', $log['memo'], $matches)) {
                    $refund_no = $matches[1];
                    $remarks = $log['memo'];
                    break;
                }
            }
            
            $result[] = array(
                '订单号' => $invoice['order_bn'],
                '备注' => $remarks,
                '红票号' => $invoice['invoice_no_red'],
                '开红时间' => $invoice['red_dateline'],
                '退款单号' => $refund_no
            );
        }
    }
    
    return $result;
}

// 输出到CSV文件
function outputToCSV($data, $filename = 'invoice_refund_data.csv') {
    if (empty($data)) {
        echo "没有找到符合条件的记录\n";
        return;
    }
    
    $filepath = dirname(__FILE__) . '/' . $filename;
    
    // 创建CSV文件
    $fp = fopen($filepath, 'w');
    if (!$fp) {
        echo "无法创建文件: $filepath\n";
        return;
    }
    
    // 写入UTF-8 BOM，确保中文正确显示
    fwrite($fp, "\xEF\xBB\xBF");
    
    // 写入表头
    $headers = array('订单号', '备注', '红票号', '开红时间', '退款单号');
    fputcsv($fp, $headers);
    
    // 写入数据
    foreach ($data as $row) {
        $csv_row = array(
            $row['订单号'],
            $row['备注'],
            $row['红票号'],
            $row['开红时间'] ? date('Y-m-d H:i:s', $row['开红时间']) : '',
            $row['退款单号']
        );
        fputcsv($fp, $csv_row);
    }
    
    fclose($fp);
    echo "数据已导出到文件: $filepath\n";
    echo "共导出 " . count($data) . " 条记录\n";
}

// 主执行逻辑
try {
    echo "开始查询发票退款数据...\n";
    
    // 查询数据
    $result = queryInvoiceWithRefundData($orderBns);
    
    if (empty($result)) {
        echo "没有找到符合条件的记录\n";
        exit;
    }
    
    echo "找到 " . count($result) . " 条符合条件的记录\n";
    
    // 输出到CSV文件
    $filename = 'invoice_refund_data_' . date('Ymd_His') . '.csv';
    outputToCSV($result, $filename);
    
    // 显示前几条记录作为预览
    echo "\n数据预览（前5条）:\n";
    echo str_repeat('-', 120) . "\n";
    echo sprintf("%-20s %-15s %-15s %-20s %-20s\n", '订单号', '红票号', '退款单号', '开红时间', '备注');
    echo str_repeat('-', 120) . "\n";
    
    $count = 0;
    foreach ($result as $row) {
        if ($count >= 5) break;
        
        $red_time = $row['开红时间'] ? date('Y-m-d H:i:s', $row['开红时间']) : '';
        $remarks = mb_substr($row['备注'], 0, 20, 'UTF-8') . (mb_strlen($row['备注'], 'UTF-8') > 20 ? '...' : '');
        
        echo sprintf("%-20s %-15s %-20s %-20s %-20s\n", 
            $row['订单号'], 
            $row['红票号'], 
            $row['退款单号'],
            $red_time,
            $remarks
        );
        $count++;
    }
    
} catch (Exception $e) {
    echo "执行出错: " . $e->getMessage() . "\n";
    exit(1);
}

echo "\n脚本执行完成！\n";